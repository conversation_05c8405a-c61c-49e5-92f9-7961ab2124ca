/**
 * Restaurant Management JavaScript
 * Handles restaurant pricing profile management
 */

class RestaurantManagement {
    constructor() {
        this.restaurants = [];
        this.filteredRestaurants = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadRestaurants();
        this.loadRestaurantOptions();
    }

    bindEvents() {
        // Search and filter events
        document.getElementById('searchRestaurants').addEventListener('input', () => this.filterRestaurants());
        document.getElementById('filterProfileType').addEventListener('change', () => this.filterRestaurants());
        document.getElementById('filterStatus').addEventListener('change', () => this.filterRestaurants());

        // Form submissions
        document.getElementById('createProfileForm').addEventListener('submit', (e) => this.handleCreateProfile(e));
        document.getElementById('editProfileForm').addEventListener('submit', (e) => this.handleEditProfile(e));
        document.getElementById('calculatorForm').addEventListener('submit', (e) => this.handleCalculation(e));
    }

    async loadRestaurants() {
        try {
            this.showLoading(true);
            
            const response = await fetch('/api/unified-pricing/restaurants', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();
            
            if (data.success) {
                this.restaurants = data.data;
                this.filteredRestaurants = [...this.restaurants];
                this.updateStatistics();
                this.renderRestaurantsTable();
            } else {
                this.showError('فشل في تحميل بيانات المطاعم: ' + data.message);
            }
        } catch (error) {
            console.error('Error loading restaurants:', error);
            this.showError('حدث خطأ أثناء تحميل بيانات المطاعم');
        } finally {
            this.showLoading(false);
        }
    }

    async loadRestaurantOptions() {
        try {
            // Load restaurants without profiles for the create modal
            const restaurantsWithoutProfiles = this.restaurants.filter(r => !r.has_profile);
            const select = document.getElementById('restaurant_id');
            
            select.innerHTML = '<option value="">اختر المطعم</option>';
            
            restaurantsWithoutProfiles.forEach(restaurant => {
                const option = document.createElement('option');
                option.value = restaurant.restaurant_id;
                option.textContent = restaurant.restaurant_name;
                select.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading restaurant options:', error);
        }
    }

    filterRestaurants() {
        const searchTerm = document.getElementById('searchRestaurants').value.toLowerCase();
        const profileTypeFilter = document.getElementById('filterProfileType').value;
        const statusFilter = document.getElementById('filterStatus').value;

        this.filteredRestaurants = this.restaurants.filter(restaurant => {
            const matchesSearch = restaurant.restaurant_name.toLowerCase().includes(searchTerm) ||
                                restaurant.restaurant_email.toLowerCase().includes(searchTerm);
            
            const matchesProfileType = !profileTypeFilter || 
                                     (restaurant.profile && restaurant.profile.profile_type === profileTypeFilter);
            
            const matchesStatus = !statusFilter || 
                                (statusFilter === 'active' && restaurant.profile && restaurant.profile.is_active) ||
                                (statusFilter === 'inactive' && (!restaurant.profile || !restaurant.profile.is_active));

            return matchesSearch && matchesProfileType && matchesStatus;
        });

        this.renderRestaurantsTable();
    }

    renderRestaurantsTable() {
        const tbody = document.getElementById('restaurantsTableBody');
        const noDataMessage = document.getElementById('noDataMessage');
        
        if (this.filteredRestaurants.length === 0) {
            tbody.innerHTML = '';
            noDataMessage.style.display = 'block';
            return;
        }

        noDataMessage.style.display = 'none';
        
        tbody.innerHTML = this.filteredRestaurants.map(restaurant => {
            const profile = restaurant.profile;
            const hasProfile = restaurant.has_profile;
            
            return `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                                <i class="fas fa-store text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">${restaurant.restaurant_name}</h6>
                                <small class="text-muted">${restaurant.restaurant_id}</small>
                            </div>
                        </div>
                    </td>
                    <td>${restaurant.restaurant_email}</td>
                    <td>
                        ${hasProfile ? 
                            `<span class="badge bg-${this.getProfileTypeBadgeColor(profile.profile_type)}">${this.getProfileTypeLabel(profile.profile_type)}</span>` :
                            '<span class="badge bg-secondary">لا يوجد ملف</span>'
                        }
                    </td>
                    <td>
                        ${hasProfile ? 
                            `<div class="commission-rates">
                                <small>إدارة: ${profile.effective_rates.admin_commission_rate}%</small><br>
                                <small>سائق: ${profile.effective_rates.driver_commission_rate}%</small>
                            </div>` :
                            '<span class="text-muted">-</span>'
                        }
                    </td>
                    <td>
                        ${hasProfile ? 
                            `<span class="text-primary">${profile.effective_rates.delivery_base_charge} ر.س</span>` :
                            '<span class="text-muted">-</span>'
                        }
                    </td>
                    <td>
                        ${hasProfile ? 
                            `<span class="badge bg-${profile.status.color}">${profile.status.message}</span>` :
                            '<span class="badge bg-warning">غير مفعل</span>'
                        }
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            ${hasProfile ? 
                                `<button type="button" class="btn btn-outline-primary" onclick="restaurantMgmt.editProfile('${profile.id}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="restaurantMgmt.showCalculator('${restaurant.restaurant_id}')">
                                    <i class="fas fa-calculator"></i>
                                </button>` :
                                `<button type="button" class="btn btn-outline-success" onclick="restaurantMgmt.createProfileForRestaurant('${restaurant.restaurant_id}')">
                                    <i class="fas fa-plus"></i> إنشاء ملف
                                </button>`
                            }
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    updateStatistics() {
        const totalRestaurants = this.restaurants.length;
        const activeProfiles = this.restaurants.filter(r => r.has_profile && r.profile.is_active).length;
        const customProfiles = this.restaurants.filter(r => r.has_profile && r.profile.profile_type === 'custom').length;
        const standardProfiles = this.restaurants.filter(r => r.has_profile && r.profile.profile_type === 'standard').length;

        document.getElementById('totalRestaurants').textContent = totalRestaurants;
        document.getElementById('activeProfiles').textContent = activeProfiles;
        document.getElementById('customProfiles').textContent = customProfiles;
        document.getElementById('standardProfiles').textContent = standardProfiles;
    }

    async handleCreateProfile(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            const response = await fetch('/api/unified-pricing/restaurants/profiles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('تم إنشاء ملف التسعير بنجاح');
                bootstrap.Modal.getInstance(document.getElementById('createProfileModal')).hide();
                e.target.reset();
                this.loadRestaurants();
                this.loadRestaurantOptions();
            } else {
                this.showError('فشل في إنشاء ملف التسعير: ' + result.message);
            }
        } catch (error) {
            console.error('Error creating profile:', error);
            this.showError('حدث خطأ أثناء إنشاء ملف التسعير');
        }
    }

    async handleEditProfile(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            const profileId = data.profile_id;
            delete data.profile_id;
            
            // Convert checkbox to boolean
            data.is_active = document.getElementById('edit_is_active').checked;
            
            const response = await fetch(`/api/unified-pricing/restaurants/profiles/${profileId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('تم تحديث ملف التسعير بنجاح');
                bootstrap.Modal.getInstance(document.getElementById('editProfileModal')).hide();
                this.loadRestaurants();
            } else {
                this.showError('فشل في تحديث ملف التسعير: ' + result.message);
            }
        } catch (error) {
            console.error('Error updating profile:', error);
            this.showError('حدث خطأ أثناء تحديث ملف التسعير');
        }
    }

    async handleCalculation(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            const response = await fetch('/api/unified-pricing/restaurants/calculate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.displayCalculationResults(result.data);
            } else {
                this.showError('فشل في حساب التسعير: ' + result.message);
            }
        } catch (error) {
            console.error('Error calculating pricing:', error);
            this.showError('حدث خطأ أثناء حساب التسعير');
        }
    }

    displayCalculationResults(data) {
        const resultsDiv = document.getElementById('calculationResults');
        const tbody = document.getElementById('resultsTableBody');
        
        tbody.innerHTML = `
            <tr><td><strong>إجمالي الطلب:</strong></td><td>${data.order_total} ر.س</td></tr>
            <tr><td><strong>رسوم التوصيل:</strong></td><td>${data.delivery_charge} ر.س</td></tr>
            <tr><td><strong>عمولة الإدارة:</strong></td><td>${data.admin_commission} ر.س</td></tr>
            <tr><td><strong>عمولة السائق:</strong></td><td>${data.driver_commission} ر.س</td></tr>
            <tr><td><strong>أرباح المطعم:</strong></td><td>${data.restaurant_earnings} ر.س</td></tr>
            <tr class="table-primary"><td><strong>إجمالي المبلغ للعميل:</strong></td><td><strong>${data.total_customer_charge} ر.س</strong></td></tr>
        `;
        
        resultsDiv.style.display = 'block';
    }

    // Utility methods
    getProfileTypeBadgeColor(type) {
        const colors = {
            'standard': 'primary',
            'premium': 'success',
            'custom': 'warning'
        };
        return colors[type] || 'secondary';
    }

    getProfileTypeLabel(type) {
        const labels = {
            'standard': 'قياسي',
            'premium': 'مميز',
            'custom': 'مخصص'
        };
        return labels[type] || type;
    }

    createProfileForRestaurant(restaurantId) {
        document.getElementById('restaurant_id').value = restaurantId;
        new bootstrap.Modal(document.getElementById('createProfileModal')).show();
    }

    async editProfile(profileId) {
        try {
            // Find the restaurant with this profile
            const restaurant = this.restaurants.find(r => r.profile && r.profile.id === profileId);
            if (!restaurant) return;

            const profile = restaurant.profile;
            
            // Populate edit form
            document.getElementById('edit_profile_id').value = profileId;
            document.getElementById('edit_profile_name').value = profile.profile_name;
            document.getElementById('edit_description').value = profile.description || '';
            document.getElementById('edit_custom_admin_commission_rate').value = profile.custom_admin_commission_rate || '';
            document.getElementById('edit_custom_driver_commission_rate').value = profile.custom_driver_commission_rate || '';
            document.getElementById('edit_custom_delivery_base_charge').value = profile.custom_delivery_base_charge || '';
            document.getElementById('edit_is_active').checked = profile.is_active;
            
            new bootstrap.Modal(document.getElementById('editProfileModal')).show();
        } catch (error) {
            console.error('Error loading profile for edit:', error);
            this.showError('حدث خطأ أثناء تحميل بيانات الملف');
        }
    }

    showCalculator(restaurantId) {
        document.getElementById('calc_restaurant_id').value = restaurantId;
        document.getElementById('calculationResults').style.display = 'none';
        document.getElementById('calculatorForm').reset();
        document.getElementById('calc_restaurant_id').value = restaurantId; // Reset removes this
        new bootstrap.Modal(document.getElementById('calculatorModal')).show();
    }

    showLoading(show) {
        document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
        document.getElementById('restaurantsTable').style.display = show ? 'none' : 'table';
    }

    showSuccess(message) {
        // You can implement a toast notification system here
        alert(message);
    }

    showError(message) {
        // You can implement a toast notification system here
        alert(message);
    }
}

// Global functions
function resetFilters() {
    document.getElementById('searchRestaurants').value = '';
    document.getElementById('filterProfileType').value = '';
    document.getElementById('filterStatus').value = '';
    restaurantMgmt.filterRestaurants();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.restaurantMgmt = new RestaurantManagement();
});
