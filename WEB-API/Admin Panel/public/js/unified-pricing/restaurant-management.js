/**
 * Restaurant Management JavaScript
 * Handles restaurant pricing profile management
 */

class RestaurantManagement {
    constructor() {
        this.restaurants = [];
        this.filteredRestaurants = [];
        this.selectedRestaurants = new Set();
        this.bulkMode = false;
        this.commissionProfiles = [];
        this.init();
    }

    init() {
        console.log('Initializing Restaurant Management...');

        // Check if required DOM elements exist
        const requiredElements = [
            'restaurantsTableBody',
            'noDataMessage',
            'searchRestaurants',
            'filterProfileType',
            'filterStatus'
        ];

        const missingElements = requiredElements.filter(id => !document.getElementById(id));
        if (missingElements.length > 0) {
            console.error('Missing required DOM elements:', missingElements);
        }

        this.bindEvents();
        this.loadRestaurants();
        this.loadCommissionProfiles();
        this.setupResponsiveHandlers();
    }

    bindEvents() {
        // Search and filter events
        document.getElementById('searchRestaurants').addEventListener('input', () => this.filterRestaurants());
        document.getElementById('filterProfileType').addEventListener('change', () => this.filterRestaurants());
        document.getElementById('filterStatus').addEventListener('change', () => this.filterRestaurants());

        // Form submissions
        document.getElementById('createProfileForm').addEventListener('submit', (e) => this.handleCreateProfile(e));
        document.getElementById('editProfileForm').addEventListener('submit', (e) => this.handleEditProfile(e));
        document.getElementById('calculatorForm').addEventListener('submit', (e) => this.handleCalculation(e));

        // Bulk selection
        document.getElementById('selectAll').addEventListener('change', (e) => this.handleSelectAll(e));

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
    }

    setupResponsiveHandlers() {
        // Handle window resize for responsive adjustments
        window.addEventListener('resize', () => {
            this.adjustTableForScreenSize();
        });

        // Initial adjustment
        this.adjustTableForScreenSize();
    }

    adjustTableForScreenSize() {
        const table = document.getElementById('restaurantsTable');
        const screenWidth = window.innerWidth;

        // Adjust table columns based on screen size
        if (screenWidth < 768) {
            // Mobile view - hide less important columns
            table.classList.add('table-sm');
        } else {
            table.classList.remove('table-sm');
        }
    }

    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + A to select all
        if ((e.ctrlKey || e.metaKey) && e.key === 'a' && this.bulkMode) {
            e.preventDefault();
            this.selectAllRestaurants();
        }

        // Escape to exit bulk mode
        if (e.key === 'Escape' && this.bulkMode) {
            this.toggleBulkMode();
        }
    }

    async loadRestaurants() {
        try {
            console.log('Starting to load restaurants...');
            this.showLoading(true);

            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (!csrfToken) {
                console.error('CSRF token not found');
                this.showError('خطأ في الأمان: لم يتم العثور على رمز CSRF');
                return;
            }

            console.log('Making API request to /api/unified-pricing/restaurants');
            const response = await fetch('/api/unified-pricing/restaurants', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                }
            });

            console.log('API response status:', response.status);
            const data = await response.json();
            console.log('API response data:', data);

            if (data.success) {
                this.restaurants = data.data;
                this.filteredRestaurants = [...this.restaurants];
                console.log(`Loaded ${this.restaurants.length} restaurants`);
                this.updateStatistics();
                this.renderRestaurantsTable();
                this.loadRestaurantOptions(); // Reload options after getting restaurant data
            } else {
                console.error('API returned error:', data.message);
                this.showError('فشل في تحميل بيانات المطاعم: ' + data.message);
            }
        } catch (error) {
            console.error('Error loading restaurants:', error);
            this.showError('حدث خطأ أثناء تحميل بيانات المطاعم: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    async loadRestaurantOptions() {
        try {
            // Load ALL restaurants for the create modal (allow creating additional custom profiles)
            const select = document.getElementById('restaurant_id');

            if (!select) {
                console.warn('Restaurant select element not found');
                return;
            }

            select.innerHTML = '<option value="">اختر المطعم</option>';

            // Show all restaurants, with indicators for existing profiles
            this.restaurants.forEach(restaurant => {
                const option = document.createElement('option');
                option.value = restaurant.restaurant_id;

                // Add indicator if restaurant already has a profile
                const profileIndicator = restaurant.has_profile ? ' (يوجد ملف حالي)' : '';
                option.textContent = restaurant.restaurant_name + profileIndicator;

                // Optionally disable restaurants that already have profiles
                // option.disabled = restaurant.has_profile;

                select.appendChild(option);
            });

            console.log(`Loaded ${this.restaurants.length} restaurant options`);
        } catch (error) {
            console.error('Error loading restaurant options:', error);
        }
    }

    filterRestaurants() {
        const searchTerm = document.getElementById('searchRestaurants').value.toLowerCase();
        const profileTypeFilter = document.getElementById('filterProfileType').value;
        const statusFilter = document.getElementById('filterStatus').value;

        this.filteredRestaurants = this.restaurants.filter(restaurant => {
            const matchesSearch = restaurant.restaurant_name.toLowerCase().includes(searchTerm) ||
                                restaurant.restaurant_email.toLowerCase().includes(searchTerm);
            
            const matchesProfileType = !profileTypeFilter || 
                                     (restaurant.profile && restaurant.profile.profile_type === profileTypeFilter);
            
            const matchesStatus = !statusFilter || 
                                (statusFilter === 'active' && restaurant.profile && restaurant.profile.is_active) ||
                                (statusFilter === 'inactive' && (!restaurant.profile || !restaurant.profile.is_active));

            return matchesSearch && matchesProfileType && matchesStatus;
        });

        this.renderRestaurantsTable();
    }

    renderRestaurantsTable() {
        console.log('Rendering restaurants table...');
        const tbody = document.getElementById('restaurantsTableBody');
        const noDataMessage = document.getElementById('noDataMessage');

        if (!tbody) {
            console.error('restaurantsTableBody element not found');
            return;
        }

        if (!noDataMessage) {
            console.error('noDataMessage element not found');
            return;
        }

        console.log(`Filtered restaurants count: ${this.filteredRestaurants.length}`);

        if (this.filteredRestaurants.length === 0) {
            tbody.innerHTML = '';
            noDataMessage.style.display = 'block';
            console.log('No restaurants to display');
            return;
        }

        noDataMessage.style.display = 'none';
        console.log('Rendering table with restaurants...');

        tbody.innerHTML = this.filteredRestaurants.map(restaurant => {
            const profile = restaurant.profile;
            const hasProfile = restaurant.has_profile;
            const isSelected = this.selectedRestaurants.has(restaurant.restaurant_id);
            const rowClass = isSelected ? 'selected-row' : '';

            return `
                <tr class="${rowClass} fade-in">
                    ${this.bulkMode ? `
                        <td>
                            <input type="checkbox" class="form-check-input bulk-select-checkbox"
                                   ${isSelected ? 'checked' : ''}
                                   onchange="restaurantMgmt.toggleRestaurantSelection('${restaurant.restaurant_id}')">
                        </td>
                    ` : ''}
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center me-2 d-none d-md-flex">
                                <i class="fas fa-store text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-0 text-truncate" style="max-width: 150px;" title="${restaurant.restaurant_name}">
                                    ${restaurant.restaurant_name}
                                </h6>
                                <small class="text-muted d-block d-md-none">${restaurant.restaurant_email}</small>
                                <small class="text-muted d-none d-lg-block">${restaurant.restaurant_id}</small>
                            </div>
                        </div>
                    </td>
                    <td class="d-none d-md-table-cell">
                        <span class="text-truncate" style="max-width: 200px;" title="${restaurant.restaurant_email}">
                            ${restaurant.restaurant_email}
                        </span>
                    </td>
                    <td>
                        ${hasProfile ?
                            `<span class="badge bg-${this.getProfileTypeBadgeColor(profile.profile_type)} badge-custom">
                                ${this.getProfileTypeLabel(profile.profile_type)}
                            </span>` :
                            '<span class="badge bg-secondary badge-custom">لا يوجد ملف</span>'
                        }
                    </td>
                    <td class="d-none d-lg-table-cell">
                        ${hasProfile ?
                            `<div class="commission-rates">
                                <small class="d-block">إدارة: ${profile.effective_rates.admin_commission_rate}%</small>
                                <small class="d-block">سائق: ${profile.effective_rates.driver_commission_rate}%</small>
                            </div>` :
                            '<span class="text-muted">-</span>'
                        }
                    </td>
                    <td class="d-none d-lg-table-cell">
                        ${hasProfile ?
                            `<span class="text-primary fw-bold">${profile.effective_rates.delivery_base_charge} ر.س</span>` :
                            '<span class="text-muted">-</span>'
                        }
                    </td>
                    <td>
                        ${hasProfile ?
                            `<span class="badge bg-${profile.status.color} badge-custom">${profile.status.message}</span>` :
                            '<span class="badge bg-warning badge-custom">غير مفعل</span>'
                        }
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm" role="group">
                            ${hasProfile ?
                                `<button type="button" class="btn btn-outline-primary btn-sm"
                                         onclick="restaurantMgmt.editProfile('${profile.id}')"
                                         title="تعديل الملف">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info btn-sm"
                                         onclick="restaurantMgmt.showCalculator('${restaurant.restaurant_id}')"
                                         title="حاسبة التسعير">
                                    <i class="fas fa-calculator"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm"
                                         onclick="restaurantMgmt.showMetrics('${restaurant.restaurant_id}')"
                                         title="مقاييس الأداء">
                                    <i class="fas fa-chart-line"></i>
                                </button>` :
                                `<button type="button" class="btn btn-outline-success btn-sm"
                                         onclick="restaurantMgmt.createProfileForRestaurant('${restaurant.restaurant_id}')"
                                         title="إنشاء ملف">
                                    <i class="fas fa-plus"></i>
                                    <span class="d-none d-md-inline ms-1">إنشاء ملف</span>
                                </button>`
                            }
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        // Update bulk selection state
        if (this.bulkMode) {
            this.updateSelectAllState();
        }
    }

    updateStatistics() {
        const totalRestaurants = this.restaurants.length;
        const activeProfiles = this.restaurants.filter(r => r.has_profile && r.profile.is_active).length;
        const customProfiles = this.restaurants.filter(r => r.has_profile && r.profile.profile_type === 'custom').length;
        const standardProfiles = this.restaurants.filter(r => r.has_profile && r.profile.profile_type === 'standard').length;

        document.getElementById('totalRestaurants').textContent = totalRestaurants;
        document.getElementById('activeProfiles').textContent = activeProfiles;
        document.getElementById('customProfiles').textContent = customProfiles;
        document.getElementById('standardProfiles').textContent = standardProfiles;
    }

    async handleCreateProfile(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            const response = await fetch('/api/unified-pricing/restaurants/profiles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('تم إنشاء ملف التسعير بنجاح');
                bootstrap.Modal.getInstance(document.getElementById('createProfileModal')).hide();
                e.target.reset();
                this.loadRestaurants();
                this.loadRestaurantOptions();
            } else {
                this.showError('فشل في إنشاء ملف التسعير: ' + result.message);
            }
        } catch (error) {
            console.error('Error creating profile:', error);
            this.showError('حدث خطأ أثناء إنشاء ملف التسعير');
        }
    }

    async handleEditProfile(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            const profileId = data.profile_id;
            delete data.profile_id;
            
            // Convert checkbox to boolean
            data.is_active = document.getElementById('edit_is_active').checked;
            
            const response = await fetch(`/api/unified-pricing/restaurants/profiles/${profileId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('تم تحديث ملف التسعير بنجاح');
                bootstrap.Modal.getInstance(document.getElementById('editProfileModal')).hide();
                this.loadRestaurants();
            } else {
                this.showError('فشل في تحديث ملف التسعير: ' + result.message);
            }
        } catch (error) {
            console.error('Error updating profile:', error);
            this.showError('حدث خطأ أثناء تحديث ملف التسعير');
        }
    }

    async handleCalculation(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            const response = await fetch('/api/unified-pricing/restaurants/calculate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();
            
            if (result.success) {
                this.displayCalculationResults(result.data);
            } else {
                this.showError('فشل في حساب التسعير: ' + result.message);
            }
        } catch (error) {
            console.error('Error calculating pricing:', error);
            this.showError('حدث خطأ أثناء حساب التسعير');
        }
    }

    displayCalculationResults(data) {
        const resultsDiv = document.getElementById('calculationResults');
        const tbody = document.getElementById('resultsTableBody');
        
        tbody.innerHTML = `
            <tr><td><strong>إجمالي الطلب:</strong></td><td>${data.order_total} ر.س</td></tr>
            <tr><td><strong>رسوم التوصيل:</strong></td><td>${data.delivery_charge} ر.س</td></tr>
            <tr><td><strong>عمولة الإدارة:</strong></td><td>${data.admin_commission} ر.س</td></tr>
            <tr><td><strong>عمولة السائق:</strong></td><td>${data.driver_commission} ر.س</td></tr>
            <tr><td><strong>أرباح المطعم:</strong></td><td>${data.restaurant_earnings} ر.س</td></tr>
            <tr class="table-primary"><td><strong>إجمالي المبلغ للعميل:</strong></td><td><strong>${data.total_customer_charge} ر.س</strong></td></tr>
        `;
        
        resultsDiv.style.display = 'block';
    }

    // Utility methods
    getProfileTypeBadgeColor(type) {
        const colors = {
            'standard': 'primary',
            'premium': 'success',
            'custom': 'warning'
        };
        return colors[type] || 'secondary';
    }

    getProfileTypeLabel(type) {
        const labels = {
            'standard': 'قياسي',
            'premium': 'مميز',
            'custom': 'مخصص'
        };
        return labels[type] || type;
    }

    createProfileForRestaurant(restaurantId) {
        document.getElementById('restaurant_id').value = restaurantId;
        new bootstrap.Modal(document.getElementById('createProfileModal')).show();
    }

    async editProfile(profileId) {
        try {
            // Find the restaurant with this profile
            const restaurant = this.restaurants.find(r => r.profile && r.profile.id === profileId);
            if (!restaurant) return;

            const profile = restaurant.profile;
            
            // Populate edit form
            document.getElementById('edit_profile_id').value = profileId;
            document.getElementById('edit_profile_name').value = profile.profile_name;
            document.getElementById('edit_description').value = profile.description || '';
            document.getElementById('edit_custom_admin_commission_rate').value = profile.custom_admin_commission_rate || '';
            document.getElementById('edit_custom_driver_commission_rate').value = profile.custom_driver_commission_rate || '';
            document.getElementById('edit_custom_delivery_base_charge').value = profile.custom_delivery_base_charge || '';
            document.getElementById('edit_is_active').checked = profile.is_active;
            
            new bootstrap.Modal(document.getElementById('editProfileModal')).show();
        } catch (error) {
            console.error('Error loading profile for edit:', error);
            this.showError('حدث خطأ أثناء تحميل بيانات الملف');
        }
    }

    showCalculator(restaurantId) {
        document.getElementById('calc_restaurant_id').value = restaurantId;
        document.getElementById('calculationResults').style.display = 'none';
        document.getElementById('calculatorForm').reset();
        document.getElementById('calc_restaurant_id').value = restaurantId; // Reset removes this
        new bootstrap.Modal(document.getElementById('calculatorModal')).show();
    }

    async showMetrics(restaurantId) {
        try {
            const response = await fetch(`/api/unified-pricing/restaurants/${restaurantId}/metrics`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const result = await response.json();

            if (result.success) {
                this.displayMetricsModal(result.data);
            } else {
                this.showError('فشل في جلب مقاييس الأداء: ' + result.message);
            }
        } catch (error) {
            console.error('Error loading metrics:', error);
            this.showError('حدث خطأ أثناء جلب مقاييس الأداء');
        }
    }

    displayMetricsModal(metrics) {
        // Create metrics modal if it doesn't exist
        let modal = document.getElementById('metricsModal');
        if (!modal) {
            modal = this.createMetricsModal();
            document.body.appendChild(modal);
        }

        // Populate modal with metrics data
        const restaurant = metrics.restaurant_info;
        const performance = metrics.performance_metrics;

        document.getElementById('metricsRestaurantName').textContent = restaurant.name;
        document.getElementById('metricsTotalOrders').textContent = performance.total_orders;
        document.getElementById('metricsTotalRevenue').textContent = performance.total_revenue.toFixed(2) + ' ر.س';
        document.getElementById('metricsCommissionPaid').textContent = performance.total_commission_paid.toFixed(2) + ' ر.س';
        document.getElementById('metricsAverageOrder').textContent = performance.average_order_value.toFixed(2) + ' ر.س';
        document.getElementById('metricsCommissionRate').textContent = performance.commission_rate + '%';
        document.getElementById('metricsRating').textContent = performance.rating.toFixed(1);

        new bootstrap.Modal(modal).show();
    }

    createMetricsModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'metricsModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">مقاييس أداء المطعم</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <h6 id="metricsRestaurantName" class="mb-3"></h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 id="metricsTotalOrders" class="text-primary">0</h4>
                                        <p class="mb-0">إجمالي الطلبات</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 id="metricsTotalRevenue" class="text-success">0 ر.س</h4>
                                        <p class="mb-0">إجمالي الإيرادات</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mt-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 id="metricsCommissionPaid" class="text-warning">0 ر.س</h4>
                                        <p class="mb-0">العمولة المدفوعة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mt-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 id="metricsAverageOrder" class="text-info">0 ر.س</h4>
                                        <p class="mb-0">متوسط قيمة الطلب</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mt-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 id="metricsCommissionRate" class="text-secondary">0%</h4>
                                        <p class="mb-0">معدل العمولة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mt-3">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <h4 id="metricsRating" class="text-warning">0.0</h4>
                                        <p class="mb-0">التقييم</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        `;
        return modal;
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        const table = document.getElementById('restaurantsTable');

        if (show) {
            spinner.style.display = 'block';
            table.style.opacity = '0.5';
            table.style.pointerEvents = 'none';
        } else {
            spinner.style.display = 'none';
            table.style.opacity = '1';
            table.style.pointerEvents = 'auto';
        }
    }

    showSuccess(message) {
        // You can implement a toast notification system here
        alert(message);
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showToast(message, type = 'info') {
        // Create toast container if it doesn't exist
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container';
            document.body.appendChild(container);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        container.appendChild(toast);

        // Initialize and show toast
        const bsToast = new bootstrap.Toast(toast, { delay: 5000 });
        bsToast.show();

        // Remove toast element after it's hidden
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    // Bulk Operations Methods
    toggleBulkMode() {
        this.bulkMode = !this.bulkMode;
        this.selectedRestaurants.clear();

        const bulkHeader = document.getElementById('bulkSelectHeader');
        const bulkBar = document.getElementById('bulkOperationsBar');
        const selectAll = document.getElementById('selectAll');

        if (this.bulkMode) {
            bulkHeader.classList.remove('d-none');
            bulkBar.style.display = 'block';
            selectAll.checked = false;
        } else {
            bulkHeader.classList.add('d-none');
            bulkBar.style.display = 'none';
        }

        this.renderRestaurantsTable();
        this.updateSelectedCount();
    }

    handleSelectAll(e) {
        const isChecked = e.target.checked;
        this.selectedRestaurants.clear();

        if (isChecked) {
            this.filteredRestaurants.forEach(restaurant => {
                this.selectedRestaurants.add(restaurant.restaurant_id);
            });
        }

        this.renderRestaurantsTable();
        this.updateSelectedCount();
    }

    toggleRestaurantSelection(restaurantId) {
        if (this.selectedRestaurants.has(restaurantId)) {
            this.selectedRestaurants.delete(restaurantId);
        } else {
            this.selectedRestaurants.add(restaurantId);
        }

        this.updateSelectedCount();
        this.updateSelectAllState();
    }

    updateSelectedCount() {
        const count = this.selectedRestaurants.size;
        document.getElementById('selectedCount').textContent = count;
    }

    updateSelectAllState() {
        const selectAll = document.getElementById('selectAll');
        const totalVisible = this.filteredRestaurants.length;
        const selectedCount = this.selectedRestaurants.size;

        if (selectedCount === 0) {
            selectAll.indeterminate = false;
            selectAll.checked = false;
        } else if (selectedCount === totalVisible) {
            selectAll.indeterminate = false;
            selectAll.checked = true;
        } else {
            selectAll.indeterminate = true;
            selectAll.checked = false;
        }
    }

    async loadCommissionProfiles() {
        try {
            const response = await fetch('/api/unified-pricing/commission-profiles', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            const data = await response.json();

            if (data.success) {
                this.commissionProfiles = data.data;
                this.populateBulkProfileSelect();
            }
        } catch (error) {
            console.error('Error loading commission profiles:', error);
        }
    }

    populateBulkProfileSelect() {
        const select = document.getElementById('bulkProfileSelect');
        select.innerHTML = '<option value="">اختر ملف العمولة</option>';

        this.commissionProfiles.forEach(profile => {
            const option = document.createElement('option');
            option.value = profile.id;
            option.textContent = profile.display_name;
            select.appendChild(option);
        });
    }

    async autoAssignProfiles() {
        if (!confirm('هل أنت متأكد من تعيين ملفات العمولة تلقائياً لجميع المطاعم؟')) {
            return;
        }

        try {
            this.showLoading(true);

            const response = await fetch('/api/unified-pricing/restaurants/auto-assign', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    force_reassign: false
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(`تم تعيين ${result.data.successful.length} مطعم بنجاح`, 'success');
                if (result.data.failed.length > 0) {
                    this.showToast(`فشل في تعيين ${result.data.failed.length} مطعم`, 'error');
                }
                this.loadRestaurants();
            } else {
                this.showError('فشل في التعيين التلقائي: ' + result.message);
            }
        } catch (error) {
            console.error('Error auto-assigning profiles:', error);
            this.showError('حدث خطأ أثناء التعيين التلقائي');
        } finally {
            this.showLoading(false);
        }
    }

    async performBulkAssign() {
        const profileId = document.getElementById('bulkProfileSelect').value;
        if (!profileId) {
            this.showError('يرجى اختيار ملف العمولة');
            return;
        }

        if (this.selectedRestaurants.size === 0) {
            this.showError('يرجى اختيار مطعم واحد على الأقل');
            return;
        }

        if (!confirm(`هل أنت متأكد من تعيين الملف المحدد لـ ${this.selectedRestaurants.size} مطعم؟`)) {
            return;
        }

        try {
            this.showLoading(true);

            const response = await fetch('/api/unified-pricing/restaurants/bulk-operations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    operation: 'assign_profile',
                    restaurant_ids: Array.from(this.selectedRestaurants),
                    profile_id: profileId,
                    reason: 'Bulk assignment by admin'
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(`تم تعيين الملف لـ ${result.data.successful.length} مطعم بنجاح`, 'success');
                if (result.data.failed.length > 0) {
                    this.showToast(`فشل في تعيين ${result.data.failed.length} مطعم`, 'error');
                }
                this.toggleBulkMode();
                this.loadRestaurants();
            } else {
                this.showError('فشل في العملية المجمعة: ' + result.message);
            }
        } catch (error) {
            console.error('Error performing bulk assign:', error);
            this.showError('حدث خطأ أثناء العملية المجمعة');
        } finally {
            this.showLoading(false);
        }
    }

    async performBulkActivate() {
        await this.performBulkOperation('activate_profiles', 'تفعيل');
    }

    async performBulkDeactivate() {
        await this.performBulkOperation('deactivate_profiles', 'إلغاء تفعيل');
    }

    async performBulkOperation(operation, operationName) {
        if (this.selectedRestaurants.size === 0) {
            this.showError('يرجى اختيار مطعم واحد على الأقل');
            return;
        }

        if (!confirm(`هل أنت متأكد من ${operationName} ${this.selectedRestaurants.size} مطعم؟`)) {
            return;
        }

        try {
            this.showLoading(true);

            const response = await fetch('/api/unified-pricing/restaurants/bulk-operations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    operation: operation,
                    restaurant_ids: Array.from(this.selectedRestaurants),
                    reason: `Bulk ${operationName} by admin`
                })
            });

            const result = await response.json();

            if (result.success) {
                this.showToast(`تم ${operationName} ${result.data.successful.length} مطعم بنجاح`, 'success');
                if (result.data.failed.length > 0) {
                    this.showToast(`فشل في ${operationName} ${result.data.failed.length} مطعم`, 'error');
                }
                this.toggleBulkMode();
                this.loadRestaurants();
            } else {
                this.showError(`فشل في ${operationName}: ` + result.message);
            }
        } catch (error) {
            console.error(`Error performing bulk ${operation}:`, error);
            this.showError(`حدث خطأ أثناء ${operationName}`);
        } finally {
            this.showLoading(false);
        }
    }

    exportData() {
        // Create CSV data
        const headers = ['اسم المطعم', 'البريد الإلكتروني', 'نوع الملف', 'معدل عمولة الإدارة', 'معدل عمولة السائق', 'رسوم التوصيل', 'الحالة'];
        const csvData = [headers];

        this.filteredRestaurants.forEach(restaurant => {
            const profile = restaurant.profile;
            csvData.push([
                restaurant.restaurant_name,
                restaurant.restaurant_email,
                profile ? this.getProfileTypeLabel(profile.profile_type) : 'لا يوجد ملف',
                profile ? profile.effective_rates.admin_commission_rate + '%' : '-',
                profile ? profile.effective_rates.driver_commission_rate + '%' : '-',
                profile ? profile.effective_rates.delivery_base_charge + ' ر.س' : '-',
                profile ? (profile.is_active ? 'نشط' : 'غير نشط') : 'غير مفعل'
            ]);
        });

        // Convert to CSV string
        const csvString = csvData.map(row => row.join(',')).join('\n');

        // Create and download file
        const blob = new Blob(['\ufeff' + csvString], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `restaurants_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showToast('تم تصدير البيانات بنجاح', 'success');
    }

    refreshData() {
        this.loadRestaurants();
        this.showToast('تم تحديث البيانات', 'success');
    }

    async showAnalytics() {
        try {
            // Calculate analytics from current data
            const analytics = this.calculateAnalytics();
            this.displayAnalyticsModal(analytics);
        } catch (error) {
            console.error('Error showing analytics:', error);
            this.showError('حدث خطأ أثناء عرض التحليلات');
        }
    }

    calculateAnalytics() {
        const totalRestaurants = this.restaurants.length;
        const activeRestaurants = this.restaurants.filter(r => r.has_profile && r.profile.is_active).length;
        const profileTypes = {
            standard: this.restaurants.filter(r => r.has_profile && r.profile.profile_type === 'standard').length,
            premium: this.restaurants.filter(r => r.has_profile && r.profile.profile_type === 'premium').length,
            custom: this.restaurants.filter(r => r.has_profile && r.profile.profile_type === 'custom').length,
            none: this.restaurants.filter(r => !r.has_profile).length
        };

        // Mock data for demonstration - in real implementation, this would come from API
        const mockRevenue = Math.random() * 100000 + 50000;
        const mockCommission = mockRevenue * 0.15;
        const mockAverageOrder = Math.random() * 100 + 50;

        return {
            totalRestaurants,
            activeRestaurants,
            profileTypes,
            totalRevenue: mockRevenue,
            totalCommission: mockCommission,
            averageOrder: mockAverageOrder,
            topPerforming: this.getTopPerformingRestaurants()
        };
    }

    getTopPerformingRestaurants() {
        // Mock top performing restaurants - in real implementation, this would be based on actual metrics
        return this.restaurants
            .filter(r => r.has_profile)
            .slice(0, 5)
            .map((restaurant, index) => ({
                name: restaurant.restaurant_name,
                orders: Math.floor(Math.random() * 500) + 100,
                revenue: Math.floor(Math.random() * 10000) + 5000,
                rating: (Math.random() * 2 + 3).toFixed(1)
            }))
            .sort((a, b) => b.revenue - a.revenue);
    }

    displayAnalyticsModal(analytics) {
        // Update statistics cards
        document.getElementById('analyticsActiveRestaurants').textContent = analytics.activeRestaurants;
        document.getElementById('analyticsTotalRevenue').textContent = analytics.totalRevenue.toFixed(2) + ' ر.س';
        document.getElementById('analyticsTotalCommission').textContent = analytics.totalCommission.toFixed(2) + ' ر.س';
        document.getElementById('analyticsAverageOrder').textContent = analytics.averageOrder.toFixed(2) + ' ر.س';

        // Update top performing restaurants
        const topPerformingDiv = document.getElementById('topPerformingRestaurants');
        topPerformingDiv.innerHTML = analytics.topPerforming.map((restaurant, index) => `
            <div class="d-flex justify-content-between align-items-center py-2 ${index < analytics.topPerforming.length - 1 ? 'border-bottom' : ''}">
                <div>
                    <strong>${restaurant.name}</strong>
                    <br>
                    <small class="text-muted">${restaurant.orders} طلب • تقييم ${restaurant.rating}</small>
                </div>
                <div class="text-end">
                    <strong class="text-success">${restaurant.revenue.toFixed(2)} ر.س</strong>
                </div>
            </div>
        `).join('');

        // Create profile types chart
        this.createProfileTypesChart(analytics.profileTypes);

        // Show modal
        new bootstrap.Modal(document.getElementById('analyticsModal')).show();
    }

    createProfileTypesChart(profileTypes) {
        const ctx = document.getElementById('profileTypesChart').getContext('2d');

        // Destroy existing chart if it exists
        if (this.profileChart) {
            this.profileChart.destroy();
        }

        this.profileChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['قياسي', 'مميز', 'مخصص', 'بدون ملف'],
                datasets: [{
                    data: [profileTypes.standard, profileTypes.premium, profileTypes.custom, profileTypes.none],
                    backgroundColor: [
                        '#0d6efd',
                        '#198754',
                        '#ffc107',
                        '#6c757d'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    exportAnalytics() {
        const analytics = this.calculateAnalytics();

        // Create analytics report
        const report = {
            'تاريخ التقرير': new Date().toLocaleDateString('ar-SA'),
            'إجمالي المطاعم': analytics.totalRestaurants,
            'المطاعم النشطة': analytics.activeRestaurants,
            'الملفات القياسية': analytics.profileTypes.standard,
            'الملفات المميزة': analytics.profileTypes.premium,
            'الملفات المخصصة': analytics.profileTypes.custom,
            'بدون ملف': analytics.profileTypes.none,
            'إجمالي الإيرادات': analytics.totalRevenue.toFixed(2) + ' ر.س',
            'إجمالي العمولات': analytics.totalCommission.toFixed(2) + ' ر.س',
            'متوسط قيمة الطلب': analytics.averageOrder.toFixed(2) + ' ر.س'
        };

        // Convert to CSV
        const csvData = Object.entries(report).map(([key, value]) => `${key},${value}`).join('\n');

        // Download file
        const blob = new Blob(['\ufeff' + csvData], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `restaurant_analytics_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        this.showToast('تم تصدير التحليلات بنجاح', 'success');
    }
}

// Global functions
function resetFilters() {
    document.getElementById('searchRestaurants').value = '';
    document.getElementById('filterProfileType').value = '';
    document.getElementById('filterStatus').value = '';
    restaurantMgmt.filterRestaurants();
}

function toggleBulkMode() {
    restaurantMgmt.toggleBulkMode();
}

function autoAssignProfiles() {
    restaurantMgmt.autoAssignProfiles();
}

function performBulkAssign() {
    restaurantMgmt.performBulkAssign();
}

function performBulkActivate() {
    restaurantMgmt.performBulkActivate();
}

function performBulkDeactivate() {
    restaurantMgmt.performBulkDeactivate();
}

function exportData() {
    restaurantMgmt.exportData();
}

function refreshData() {
    restaurantMgmt.refreshData();
}

function showAnalytics() {
    restaurantMgmt.showAnalytics();
}

function exportAnalytics() {
    restaurantMgmt.exportAnalytics();
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.restaurantMgmt = new RestaurantManagement();
});
