<?php

return [
    // Page titles and navigation
    'page_title' => 'Unified Pricing System',
    'breadcrumb_dashboard' => 'Dashboard',
    'breadcrumb_unified_pricing' => 'Unified Pricing System',

    // Navigation
    'navigation' => [
        'dashboard' => 'Dashboard',
        'templates' => 'Pricing Templates',
        'settings' => 'Pricing Settings',
        'reports' => 'Reports',
        'analytics' => 'Analytics',
    ],

    // Scopes
    'scope' => [
        'global' => 'Global',
        'zone' => 'Zone',
        'vendor' => 'Vendor',
        'driver' => 'Driver',
    ],
    
    // Main index page
    'index' => [
        'title' => 'Unified Pricing Settings Management',
        'subtitle' => 'Manage and configure pricing and commission settings for the system',
        'create_new' => 'Create New Setting',
        'refresh' => 'Refresh',
        'export' => 'Export',
        'import' => 'Import',
        'bulk_delete' => 'Delete Selected',
        'select_all' => 'Select All',
        'no_data' => 'No pricing settings available',
        'loading' => 'Loading...',
        'last_updated' => 'Last Updated',
        'search_placeholder' => 'Search settings...',
        'filter_scope' => 'Filter by Scope',
        'filter_status' => 'Filter by Status',
        'items_per_page' => 'Items per page',
    ],
    
    // Table headers and content
    'table' => [
        'setting_name' => 'Setting Name',
        'display_name' => 'Display Name',
        'scope' => 'Scope',
        'status' => 'Status',
        'last_updated' => 'Last Updated',
        'actions' => 'Actions',
        'edit' => 'Edit',
        'view' => 'View',
        'delete' => 'Delete',
        'duplicate' => 'Duplicate',
        'toggle_status' => 'Toggle Status',
    ],
    
    // Status labels
    'status' => [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'draft' => 'Draft',
        'pending' => 'Pending',
    ],
    
    // Scope labels
    'scope' => [
        'global' => 'Global',
        'zone' => 'Zone',
        'vendor' => 'Vendor',
        'driver' => 'Driver',
        'restaurant' => 'Restaurant',
    ],

    // Distance unit options
    'distance_unit' => [
        'km' => 'Kilometer',
        'mile' => 'Mile',
    ],

    // Delivery charge type options
    'delivery_charge_type' => [
        'fixed' => 'Fixed',
        'per_km' => 'Per Kilometer',
        'dynamic' => 'Dynamic',
        'zone_based' => 'Zone Based',
    ],

    // Commission calculation method options
    'commission_calculation_method' => [
        'percentage' => 'Percentage',
        'fixed' => 'Fixed Amount',
        'distance_based' => 'Distance Based',
        'hybrid' => 'Hybrid',
    ],
    
    // Edit page
    'edit' => [
        'title' => 'Edit Unified Pricing Setting',
        'form_title' => 'Edit Unified Pricing Setting',
        'form_subtitle' => 'Modify your unified pricing system settings through the following sections',
        'breadcrumb_edit' => 'Edit',
        'step1_title' => 'Basic Information',
        'step2_title' => 'Delivery Settings',
        'step3_title' => 'Commission Settings',
        'step4_title' => 'Advanced Settings',
        'auto_saved' => 'Auto-saved',
        'unsaved_changes' => 'Unsaved changes',
        'preview' => 'Preview',
        'compare_changes' => 'Compare Changes',
        'revert_changes' => 'Revert Changes',
        'save_draft' => 'Save Draft',
        'save_changes' => 'Save Changes',

        // Form fields - inherit from create but with edit context
        'name' => 'Setting Name',
        'name_help' => 'Unique name for the setting (in English)',
        'display_name' => 'Display Name',
        'display_name_help' => 'Name that will be shown to users',
        'description' => 'Description',
        'description_help' => 'Brief description of the setting',
        'scope' => 'Scope',
        'scope_help' => 'Application scope of the setting',
        'select_scope' => 'Select Scope',
        'status' => 'Status',
        'status_help' => 'Setting status (active, inactive, draft)',
        'select_status' => 'Select Status',
        'priority' => 'Priority',
        'priority_help' => 'Setting priority (1-100, higher has priority)',

        // Delivery settings
        'distance_unit' => 'Distance Unit',
        'distance_unit_help' => 'Unit of measurement for distance',
        'select_distance_unit' => 'Select Distance Unit',
        'delivery_charge_type' => 'Delivery Charge Type',
        'delivery_charge_type_help' => 'Method for calculating delivery charges',
        'select_delivery_charge_type' => 'Select Delivery Charge Type',
        'base_delivery_charge' => 'Base Delivery Charge',
        'base_delivery_charge_help' => 'Fixed charge for delivery',
        'per_km_delivery_rate' => 'Per KM Delivery Rate',
        'per_km_delivery_rate_help' => 'Additional charge per kilometer',
        'min_delivery_charge' => 'Minimum Delivery Charge',
        'min_delivery_charge_help' => 'Minimum amount that can be charged for delivery',
        'max_delivery_charge' => 'Maximum Delivery Charge',
        'max_delivery_charge_help' => 'Maximum amount that can be charged for delivery',
        'max_delivery_distance' => 'Maximum Delivery Distance',
        'max_delivery_distance_help' => 'Maximum distance for delivery',
        'driver_search_radius' => 'Driver Search Radius',
        'driver_search_radius_help' => 'Distance to search for drivers',

        // Commission settings
        'commission_calculation_method' => 'Commission Calculation Method',
        'commission_calculation_method_help' => 'Method used to calculate commission',
        'select_commission_calculation_method' => 'Select Commission Calculation Method',
        'admin_commission_rate' => 'Admin Commission Rate',
        'admin_commission_rate_help' => 'Commission rate for admin (percentage)',
        'driver_commission_rate' => 'Driver Commission Rate',
        'driver_commission_rate_help' => 'Commission rate for driver (percentage)',
        'vendor_commission_rate' => 'Vendor Commission Rate',
        'vendor_commission_rate_help' => 'Commission rate for vendor (percentage)',
        'driver_distance_rate' => 'Driver Distance Rate',
        'driver_distance_rate_help' => 'Amount paid to driver per kilometer',
        'commission_on_delivery_charge' => 'Commission on Delivery Charge',
        'commission_on_delivery_charge_help' => 'Apply commission on delivery charges as well',

        // Advanced settings
        'surge_multiplier' => 'Surge Multiplier',
        'surge_multiplier_help' => 'Price multiplier during surge hours',
        'auto_sync_enabled' => 'Enable Auto Sync',
        'auto_sync_enabled_help' => 'Automatically sync settings with system',

        'currency' => 'SAR',
        'km' => 'KM',

        // Messages
        'update_failed' => 'Failed to update setting',
        'updated_successfully' => 'Setting updated successfully',
        'saved_as_draft' => 'Setting saved as draft',
        'validation_failed' => 'Invalid data provided',
        'update_error' => 'Error occurred during update',
    ],

    // Create page
    'create' => [
        'title' => 'Create New Unified Pricing Setting',
        'subtitle' => 'Configure your unified pricing system easily through the following sections',
        'breadcrumb_create' => 'Create New',
        'form_title' => 'Unified Pricing Setting Creation Form',
        'form_subtitle' => 'Fill out the following form to create a new unified pricing setting',
        'step1_title' => 'Basic Information',
        'step2_title' => 'Delivery Settings',
        'step3_title' => 'Commission Settings',
        'step4_title' => 'Advanced Settings',
        'step5_title' => 'Review & Save',
        'save' => 'Save Setting',
        'save_draft' => 'Save as Draft',
        'preview' => 'Preview',
        'cancel' => 'Cancel',
        'auto_saved' => 'Auto-saved successfully',
        'auto_save_enabled' => 'Auto-saved',
        'auto_save_saving' => 'Saving...',
        'draft_restored' => 'Saved draft restored',
        'unsaved_changes' => 'You have unsaved changes. Do you want to leave?',

        // Form fields
        'name' => 'Setting Name',
        'name_help' => 'Unique name for the setting (in English)',
        'display_name' => 'Display Name',
        'display_name_help' => 'Name that will be shown to users',
        'description' => 'Description',
        'description_help' => 'Brief description of the setting',
        'scope' => 'Scope',
        'scope_help' => 'Application scope of the setting',
        'select_scope' => 'Select Scope',
        'status' => 'Status',
        'status_help' => 'Setting status (active, inactive, draft)',
        'select_status' => 'Select Status',
        'active' => 'Active',
        'priority' => 'Priority',
        'priority_help' => 'Setting priority (1-100, higher has priority)',

        // Delivery settings
        'distance_unit' => 'Distance Unit',
        'distance_unit_help' => 'Unit of measurement for distance',
        'select_distance_unit' => 'Select Distance Unit',
        'delivery_charge_type' => 'Delivery Charge Type',
        'delivery_charge_type_help' => 'Method for calculating delivery charges',
        'select_delivery_charge_type' => 'Select Delivery Charge Type',
        'base_delivery_charge' => 'Base Delivery Charge',
        'base_delivery_charge_help' => 'Fixed charge for delivery',
        'per_km_delivery_rate' => 'Per KM Delivery Rate',
        'per_km_delivery_rate_help' => 'Additional charge per kilometer',
        'min_delivery_charge' => 'Minimum Delivery Charge',
        'min_delivery_charge_help' => 'Minimum amount that can be charged for delivery',
        'max_delivery_charge' => 'Maximum Delivery Charge',
        'max_delivery_charge_help' => 'Maximum amount that can be charged for delivery',
        'max_delivery_distance' => 'Maximum Delivery Distance',
        'max_delivery_distance_help' => 'Maximum distance for delivery',
        'driver_search_radius' => 'Driver Search Radius',
        'driver_search_radius_help' => 'Distance to search for drivers',

        // Commission settings
        'commission_calculation_method' => 'Commission Calculation Method',
        'commission_calculation_method_help' => 'Method used to calculate commission',
        'select_commission_calculation_method' => 'Select Commission Calculation Method',
        'admin_commission_rate' => 'Admin Commission Rate',
        'admin_commission_rate_help' => 'Commission rate for admin (percentage)',
        'driver_commission_rate' => 'Driver Commission Rate',
        'driver_commission_rate_help' => 'Commission rate for driver (percentage)',
        'vendor_commission_rate' => 'Vendor Commission Rate',
        'vendor_commission_rate_help' => 'Commission rate for vendor (percentage)',
        'admin_fixed_commission' => 'Admin Fixed Commission',
        'admin_fixed_commission_help' => 'Fixed amount as commission for admin',
        'driver_fixed_commission' => 'Driver Fixed Commission',
        'driver_fixed_commission_help' => 'Fixed amount as commission for driver',
        'vendor_fixed_commission' => 'Vendor Fixed Commission',
        'vendor_fixed_commission_help' => 'Fixed amount as commission for vendor',
        'driver_distance_rate' => 'Driver Distance Rate',
        'driver_distance_rate_help' => 'Amount paid to driver per kilometer',
        'driver_base_rate' => 'Driver Base Rate',
        'driver_base_rate_help' => 'Base amount paid to driver',
        'commission_on_delivery_charge' => 'Commission on Delivery Charge',
        'commission_on_delivery_charge_help' => 'Apply commission on delivery charges as well',

        // Advanced settings
        'surge_multiplier' => 'Surge Multiplier',
        'surge_multiplier_help' => 'Price multiplier during surge hours',
        'weather_multiplier' => 'Weather Multiplier',
        'weather_multiplier_help' => 'Price multiplier during bad weather',
        'holiday_multiplier' => 'Holiday Multiplier',
        'holiday_multiplier_help' => 'Price multiplier during holidays',
        'night_multiplier' => 'Night Multiplier',
        'night_multiplier_help' => 'Price multiplier during night hours',
        'peak_hours_multiplier' => 'Peak Hours Multiplier',
        'peak_hours_multiplier_help' => 'Price multiplier during peak hours',
        'min_order_value' => 'Minimum Order Value',
        'min_order_value_help' => 'Minimum order value that can be accepted',
        'max_order_value' => 'Maximum Order Value',
        'max_order_value_help' => 'Maximum order value that can be accepted',
        'cache_duration' => 'Cache Duration',
        'cache_duration_help' => 'Duration to keep data in cache',
        'calculation_timeout' => 'Calculation Timeout',
        'calculation_timeout_help' => 'Maximum time for pricing calculation',
        'notification_emails' => 'Notification Emails',
        'notification_emails_help' => 'Email addresses for notifications',
        'auto_sync_enabled' => 'Enable Auto Sync',
        'auto_sync_enabled_help' => 'Automatically sync settings with system',
        'km' => 'KM',
        'minutes' => 'Minutes',
        'seconds' => 'Seconds',

        'currency' => 'SAR',
    ],
    
    // Form sections
    'sections' => [
        'basic_info' => [
            'title' => 'Basic Information',
            'subtitle' => 'Basic information for the pricing setting',
        ],
        'delivery_settings' => [
            'title' => 'Delivery Settings',
            'subtitle' => 'Configure delivery fees and rules',
        ],
        'commission_settings' => [
            'title' => 'Commission Settings',
            'subtitle' => 'Define commission rates and calculation methods',
        ],
        'advanced_settings' => [
            'title' => 'Advanced Settings',
            'subtitle' => 'Advanced settings for precise pricing control',
        ],
        'review_confirm' => [
            'title' => 'Review & Confirm',
            'subtitle' => 'Review all settings before saving',
        ],
    ],
    
    // Form fields
    'fields' => [
        'name' => [
            'label' => 'Setting Name (English)',
            'placeholder' => 'e.g., standard_delivery_pricing',
            'help' => 'Unique name for the setting in English',
        ],
        'display_name' => [
            'label' => 'Display Name',
            'placeholder' => 'e.g., Standard Delivery Pricing',
            'help' => 'Name that will appear in the interface',
        ],
        'description' => [
            'label' => 'Description',
            'placeholder' => 'Brief description of this setting...',
            'help' => 'Optional description to clarify the purpose of this setting',
        ],
        'scope' => [
            'label' => 'Application Scope',
            'help' => 'Define the scope of application for this setting',
            'options' => [
                'global' => 'Global - applies to the entire system',
                'zone' => 'Zone - applies to a specific zone',
                'vendor' => 'Vendor - applies to a specific vendor',
                'driver' => 'Driver - applies to a specific driver',
            ],
        ],
        'priority' => [
            'label' => 'Priority',
            'help' => 'Priority for applying this setting (higher number has priority)',
        ],
        'is_active' => [
            'label' => 'Active',
            'help' => 'Enable or disable this setting',
        ],
        
        // Delivery fields
        'distance_unit' => [
            'label' => 'Distance Unit',
            'options' => [
                'km' => 'Kilometer',
                'mile' => 'Mile',
            ],
        ],
        'max_delivery_distance' => [
            'label' => 'Maximum Delivery Distance',
            'help' => 'Maximum distance for delivery',
        ],
        'driver_search_radius' => [
            'label' => 'Driver Search Radius',
            'help' => 'Distance within which to search for drivers',
        ],
        'delivery_charge_type' => [
            'label' => 'Delivery Charge Type',
            'options' => [
                'fixed' => 'Fixed',
                'per_km' => 'Per Kilometer',
                'dynamic' => 'Dynamic',
                'zone_based' => 'Zone Based',
            ],
        ],
        'base_delivery_charge' => [
            'label' => 'Base Delivery Charge',
            'help' => 'Basic fixed charge for delivery',
        ],
        'per_km_delivery_rate' => [
            'label' => 'Per Kilometer Rate',
            'help' => 'Additional charge per kilometer',
        ],
        'min_delivery_charge' => [
            'label' => 'Minimum Delivery Charge',
            'help' => 'Minimum delivery charge',
        ],
        'max_delivery_charge' => [
            'label' => 'Maximum Delivery Charge',
            'help' => 'Maximum delivery charge',
        ],
        'free_delivery_above' => [
            'label' => 'Free Delivery Above',
            'help' => 'Order value above which delivery becomes free',
        ],
        'urgent_delivery_multiplier' => [
            'label' => 'Urgent Delivery Multiplier',
            'help' => 'Multiplier applied to urgent delivery charges',
        ],
        
        // Commission fields
        'commission_calculation_method' => [
            'label' => 'Commission Calculation Method',
            'options' => [
                'percentage' => 'Percentage',
                'fixed' => 'Fixed Amount',
                'hybrid' => 'Hybrid',
                'distance_based' => 'Distance Based',
            ],
        ],
        'admin_commission_rate' => [
            'label' => 'Admin Commission Rate',
            'help' => 'Admin commission rate or amount',
        ],
        'driver_commission_rate' => [
            'label' => 'Driver Commission Rate',
            'help' => 'Driver commission rate or amount',
        ],
        'driver_distance_rate' => [
            'label' => 'Driver Distance Rate',
            'help' => 'Amount paid to driver per kilometer',
        ],
        'min_driver_commission' => [
            'label' => 'Minimum Driver Commission',
            'help' => 'Minimum commission for driver',
        ],
        'max_driver_commission' => [
            'label' => 'Maximum Driver Commission',
            'help' => 'Maximum commission for driver',
        ],
        
        // Advanced fields
        'time_based_multipliers' => [
            'label' => 'Time-based Multipliers',
            'help' => 'Multipliers applied based on peak hours (JSON format)',
            'placeholder' => '{"peak_hours": {"start": "12:00", "end": "14:00", "multiplier": 1.2}}',
        ],
        'day_based_multipliers' => [
            'label' => 'Day-based Multipliers',
            'help' => 'Multipliers applied based on days of the week (JSON format)',
            'placeholder' => '{"friday": 1.1, "saturday": 1.1}',
        ],
        'zone_specific_rates' => [
            'label' => 'Zone-specific Rates',
            'help' => 'Different rates based on geographical zones (JSON format)',
            'placeholder' => '{"luxury_areas": {"multiplier": 1.5}}',
        ],
        'effective_from' => [
            'label' => 'Effective From',
            'help' => 'Start date for this setting',
        ],
        'effective_until' => [
            'label' => 'Effective Until',
            'help' => 'End date for this setting (optional)',
        ],
        'auto_sync_enabled' => [
            'label' => 'Enable Auto Sync',
            'help' => 'Automatically sync this setting with other systems',
        ],
        'version' => [
            'label' => 'Version Number',
            'help' => 'Version number of this setting',
        ],
        'tags' => [
            'label' => 'Tags',
            'placeholder' => 'e.g., peak, normal, special',
            'help' => 'Tags to categorize this setting (separate with comma)',
        ],
        'notes' => [
            'label' => 'Notes',
            'placeholder' => 'Any additional notes about this setting...',
            'help' => 'Internal notes about this setting',
        ],
    ],
    
    // Calculator and preview
    'calculator' => [
        'title' => 'Interactive Delivery Calculator',
        'order_value' => 'Order Value',
        'distance' => 'Distance',
        'delivery_cost' => 'Delivery Cost',
        'admin_commission' => 'Admin Commission',
        'driver_commission' => 'Driver Commission',
        'vendor_net' => 'Vendor Net',
        'total_customer' => 'Customer Total',
        'currency' => 'SAR',
    ],
    
    // Preview sections
    'preview' => [
        'title' => 'Setting Preview',
        'basic_info' => 'Basic Information',
        'delivery_settings' => 'Delivery Settings',
        'commission_settings' => 'Commission Settings',
        'advanced_settings' => 'Advanced Settings',
        'not_set' => 'Not set',
        'empty' => 'Empty',
    ],
    
    // Messages
    'messages' => [
        'create_success' => 'Pricing setting created successfully',
        'create_error' => 'Failed to create pricing setting',
        'update_success' => 'Pricing setting updated successfully',
        'update_error' => 'Failed to update pricing setting',
        'delete_success' => 'Pricing setting deleted successfully',
        'delete_error' => 'Failed to delete pricing setting',
        'delete_confirm' => 'Are you sure you want to delete this setting?',
        'bulk_delete_confirm' => 'Are you sure you want to delete the selected items?',
        'draft_saved' => 'Draft saved successfully',
        'draft_save_error' => 'Failed to save draft',
        'validation_error' => 'Please correct the errors in the form',
        'loading_error' => 'Failed to load data',
        'network_error' => 'Network connection error',
        'permission_denied' => 'You do not have permission to perform this action',
    ],
    
    // Analytics page
    'analytics' => [
        'title' => 'Analytics & Reports Dashboard - Unified Pricing',
        'dashboard' => 'Analytics Dashboard',
        'reports' => 'Reports',
        'metrics' => 'Metrics',
        'charts' => 'Charts',
        'export' => 'Export Report',
        'date_range' => 'Date Range',
        'filter' => 'Filter',
        'total_orders' => 'Total Orders',
        'total_revenue' => 'Total Revenue',
        'avg_delivery_cost' => 'Average Delivery Cost',
        'commission_earned' => 'Commission Earned',
    ],

    // Templates page
    'templates' => [
        'title' => 'Ready-made Pricing Templates',
        'subtitle' => 'Choose from ready-made templates to create pricing settings quickly',
        'popular' => 'Popular',
        'recommended' => 'Recommended',
        'custom' => 'Custom',
        'recent' => 'Recent',
        'all_templates' => 'All Templates',
        'all_categories' => 'All Categories',
        'all_scopes' => 'All Scopes',
        'my_templates' => 'My Templates',
        'search_placeholder' => 'Search templates...',
        'create_new_template' => 'Create New Template',
        'create_template_description' => 'Create a custom template from current settings',
        'use_template' => 'Use Template',
        'preview' => 'Preview',
        'preview_template' => 'Preview Template',
        'create_from_template' => 'Create from Template',
        'standard_delivery' => 'Standard Delivery',
        'express_delivery' => 'Express Delivery',
        'premium_delivery' => 'Premium Delivery',
        'zone_based' => 'Zone Based',
        'loading' => 'Loading templates...',
        'no_templates' => 'No Templates',
        'no_templates_description' => 'No templates found matching search criteria',
        'reset_filters' => 'Reset Filters',
        'created_successfully' => 'Template created successfully',
        'updated_successfully' => 'Template updated successfully',
        'deleted_successfully' => 'Template deleted successfully',
        'setting_created_from_template' => 'Pricing setting created from template successfully',

        // Additional success messages
        'activated_successfully' => 'Template activated successfully',
        'deactivated_successfully' => 'Template deactivated successfully',
        'used_successfully' => 'Template applied successfully',
        'used' => 'Template used successfully',
        'bulk_activated_successfully' => 'Selected templates activated successfully',
        'bulk_deactivated_successfully' => 'Selected templates deactivated successfully',
        'bulk_deleted_successfully' => 'Selected templates deleted successfully',

        // Error messages
        'not_found' => 'Template not found',
        'load_failed' => 'Failed to load templates',
        'save_failed' => 'Failed to save template',
        'delete_failed' => 'Failed to delete template',
        'update_failed' => 'Failed to update template',
        'network_error' => 'Network connection error',
        'invalid_data' => 'Invalid data provided',
        'unauthorized' => 'You do not have permission to perform this action',
        'forbidden' => 'Cannot modify predefined templates',
        'timeout' => 'Request timeout',
        'generic_error' => 'An unexpected error occurred. Please try again.',

        // Confirmation messages
        'confirm_delete' => 'Are you sure you want to delete this template? This action cannot be undone.',
        'confirm_bulk_delete' => 'Are you sure you want to delete the selected templates? This action cannot be undone.',
        'confirm_bulk_activate' => 'Do you want to activate all selected templates?',
        'confirm_bulk_deactivate' => 'Do you want to deactivate all selected templates?',
        'confirm_use' => 'Do you want to use this template to create a new pricing setting?',

        // Info messages
        'select_templates' => 'Please select at least one template',
        'no_results' => 'No templates match the search criteria',
        'processing' => 'Processing...',

        // Additional template messages
        'cannot_modify_predefined' => 'Cannot modify predefined templates',
        'cannot_delete_predefined' => 'Cannot delete predefined templates',
        'view' => 'View',
        'use' => 'Use',
        'edit' => 'Edit',
        'delete' => 'Delete',

        // New interface elements
        'index_title' => 'Pricing Templates',
        'page_title' => 'Manage Pricing Templates',
        'breadcrumb' => 'Pricing Templates',
        'create_new' => 'Create New Template',
        'create' => 'Create',
        'refresh' => 'Refresh',
        'pricing_settings' => 'Pricing Settings',
        'pricing_settings_desc' => 'Define the basic pricing settings for the template',
        'base_price' => 'Base Price',
        'per_km_rate' => 'Per KM Rate',
        'commission_rate' => 'Commission Rate',
        'min_order_value' => 'Minimum Order Value',
        'max_delivery_distance' => 'Maximum Delivery Distance',
        'ready_to_save' => 'Ready to Save?',
        'review_before_save' => 'Review the data before saving',
        'preview_short' => 'Preview',
        'save' => 'Save',
        'category_help' => 'Choose the appropriate template category',
        'scope_help' => 'Define the template application scope',
        'description_help' => 'Detailed description of the template and how to use it',
        'template_settings' => 'Template Settings',
        'bulk_actions' => 'Bulk Actions',
        'activate_selected' => 'Activate Selected',
        'deactivate_selected' => 'Deactivate Selected',
        'delete_selected' => 'Delete Selected',
        'search_placeholder' => 'Search templates...',
        'all_categories' => 'All Categories',
        'error_title' => 'Loading Error',
        'retry' => 'Retry',
        'no_templates_description' => 'No templates are currently available',
        'create_first' => 'Create First Template',
        'loading_templates' => 'Loading templates...',
        'showing_results' => 'Showing results',
        'title' => 'Pricing Templates',
        'subtitle' => 'Manage and organize different pricing templates',
        'all_scopes' => 'All Scopes',

        // Table headers
        'table' => [
            'name' => 'Template Name',
            'category' => 'Category',
            'scope' => 'Scope',
            'usage_count' => 'Usage Count',
            'rating' => 'Rating',
            'created_at' => 'Created Date',
            'actions' => 'Actions',
        ],

        // Create template modal
        'template_name' => 'Template Name',
        'template_name_placeholder' => 'Enter template name',
        'category' => 'Category',
        'select_category' => 'Select Category',
        'description' => 'Description',
        'description_placeholder' => 'Brief description of the template (optional)',
        'scope' => 'Scope',
        'select_scope' => 'Select Scope',
        'public_template' => 'Public Template (others can use it)',
        'public_template_help' => 'If enabled, this template will be available to all users',
        'save_template' => 'Save Template',

        // Template pages
        'edit_template' => 'Edit Template',
        'template_details' => 'Template Details',
        'view' => 'View',
        'template_information' => 'Template Information',
        'template_id' => 'Template ID',
        'times' => 'times',
        'last_used' => 'Last Used',
        'edit_warning' => 'Warning: This template is currently in use. Any changes may affect existing settings.',
        'basic_information' => 'Basic Information',
        'basic_information_desc' => 'Enter the basic information for the template',
        'template_name_help' => 'Choose a descriptive and clear name for the template',
        'template_name_required' => 'Template name is required',
        'category_required' => 'Template category is required',
        'scope_required' => 'Template scope is required',
        'update_template' => 'Update Template',
        'reset_form_confirm' => 'Are you sure you want to reset the form? You will lose all unsaved changes.',
        'preview_coming_soon' => 'Preview feature is under development',
        'property' => 'Property',
        'value' => 'Value',
        'visibility' => 'Visibility',
        'public' => 'Public',
        'private' => 'Private',
        'usage_statistics' => 'Usage Statistics',
        'total_uses' => 'Total Uses',
        'this_month' => 'This Month',
        'this_week' => 'This Week',
        'success_rate' => 'Success Rate',
        'pricing_settings' => 'Pricing Settings',
        'settings_preview_note' => 'This is a preview of the pricing settings saved in the template',
        'delivery_charges' => 'Delivery Charges',
        'commission_rates' => 'Commission Rates',
        'rating' => 'Rating',
        'delete_template' => 'Delete Template',
        'use_template_confirm' => 'Do you want to create a new pricing setting from this template?',
        'delete_template_confirm' => 'Are you sure you want to delete this template? This action cannot be undone.',
    ],

    // Edit page
    'edit' => [
        'title' => 'Edit Unified Pricing Setting',
        'subtitle' => 'Edit existing unified pricing settings',
        'save_changes' => 'Save Changes',
        'cancel_changes' => 'Cancel Changes',
        'unsaved_changes' => 'You have unsaved changes',
        'changes_detected' => 'Changes detected',
        'compare_changes' => 'Compare Changes',
        'original_value' => 'Original Value',
        'new_value' => 'New Value',
        'revert_changes' => 'Revert Changes',
    ],

    // Validation messages
    'validation' => [
        'required' => 'This field is required',
        'min_length' => 'Must be at least :min characters',
        'max_length' => 'Must be at most :max characters',
        'numeric' => 'Must be a valid number',
        'positive' => 'Must be greater than zero',
        'percentage' => 'Must be between 0 and 100',
        'invalid_format' => 'Invalid format',
        'invalid_json' => 'Invalid JSON format',
        'unique' => 'This value is already in use',
        'date_format' => 'Invalid date format',
        'after_date' => 'Must be after :date',
    ],

    // Commission Profiles
    'profiles' => [
        'page_title' => 'Commission Profiles',
        'breadcrumb' => 'Profiles',
        'create_new' => 'Create New Profile',
        'edit_profile' => 'Edit Commission Profile',
        'profile_details' => 'Profile Details',
        'profile_information' => 'Profile Information',
        'profile_name' => 'Profile Name',
        'display_name' => 'Display Name',
        'description' => 'Description',
        'profile_type' => 'Profile Type',
        'business_model' => 'Business Model',
        'commission_rates' => 'Commission Rates',
        'admin_commission_rate' => 'Admin Commission Rate (%)',
        'driver_commission_rate' => 'Driver Commission Rate (%)',
        'platform_fee_rate' => 'Platform Fee Rate (%)',
        'fixed_fee_per_order' => 'Fixed Fee Per Order ($)',
        'payment_processing_fee' => 'Payment Processing Fee ($)',
        'delivery_settings' => 'Delivery Settings',
        'base_delivery_charge' => 'Base Delivery Charge ($)',
        'per_km_delivery_rate' => 'Per KM Rate ($)',
        'free_delivery_threshold' => 'Free Delivery Threshold ($)',
        'min_delivery_charge' => 'Minimum Delivery Charge ($)',
        'max_delivery_charge' => 'Maximum Delivery Charge ($)',
        'eligibility_requirements' => 'Eligibility Requirements',
        'min_monthly_orders' => 'Minimum Monthly Orders',
        'min_rating_required' => 'Minimum Rating Required',
        'profile_settings' => 'Profile Settings',
        'is_active' => 'Active Profile',
        'auto_assign_eligible' => 'Auto-Assign Eligible',
        'requires_approval' => 'Requires Approval',
        'profile_statistics' => 'Profile Statistics',
        'active_restaurants' => 'Active Restaurants',
        'total_orders' => 'Total Orders',
        'commission_earned' => 'Commission Earned',
        'average_order_value' => 'Average Order Value',
        'danger_zone' => 'Danger Zone',
        'delete_profile' => 'Delete Profile',
        'delete_warning' => 'Deleting this profile will affect all restaurants currently using it.',
        'delete_confirm' => 'Are you sure you want to delete this profile? This action cannot be undone.',
        'update_profile' => 'Update Profile',
        'cancel' => 'Cancel',
        'back_to_profiles' => 'Back to Profiles',
        'profile_created' => 'Commission profile created successfully!',
        'profile_updated' => 'Profile updated successfully!',
        'profile_deleted' => 'Commission profile deleted successfully!',
        'profile_not_found' => 'Profile not found',
        'cannot_delete_in_use' => 'Cannot delete profile that is currently assigned to restaurants.',
        'validation_failed' => 'Please check the form for errors',
        'update_failed' => 'Failed to update profile',
        'create_failed' => 'Failed to create profile',
        'delete_failed' => 'Failed to delete profile',
        'no_profiles' => 'No commission profiles found.',
        'create_first_profile' => 'Create First Profile',
        'restaurants_using_profile' => 'Restaurants using this profile',
        'orders_processed' => 'Orders processed with this profile',
        'commission_from_profile' => 'Total commission from this profile',
        'avg_order_for_profile' => 'Average order value for this profile',
        'updating' => 'Updating...',
        'loading' => 'Loading...',
        'profile_information' => 'Profile Information',
        'create_description' => 'Create a new commission profile for restaurant pricing',
        'internal_name_help' => 'Internal name for the profile',
        'display_name_help' => 'Name shown to restaurants',
        'description_placeholder' => 'Enter a description for this profile...',
        'select_type' => 'Select Type',
        'select_model' => 'Select Model',
        'admin_fixed_fee' => 'Admin Fixed Fee ($)',
        'driver_fixed_fee' => 'Driver Fixed Fee ($)',
    ],

    // Profile Types
    'profile_types' => [
        'starter' => 'Starter',
        'standard' => 'Standard',
        'premium' => 'Premium',
        'enterprise' => 'Enterprise',
        'promotional' => 'Promotional',
        'custom' => 'Custom',
    ],

    // Business Models
    'business_models' => [
        'percentage' => 'Percentage',
        'fixed' => 'Fixed Amount',
        'hybrid' => 'Hybrid (Percentage + Fixed)',
        'tiered' => 'Tiered',
        'performance' => 'Performance',
    ],

    // Status messages
    'success' => 'Success',
    'failed' => 'Failed',
    'auto_assign_success' => 'Auto assignment successful',
    'auto_assign_failed' => 'Auto assignment failed',

    // Upgrade evaluation messages
    'upgrade_evaluation_complete' => 'Upgrade Evaluation Complete',
    'evaluated' => 'Evaluated',
    'upgraded' => 'Upgraded',
    'no_change' => 'No Change',
    'upgrade_evaluation_success' => 'Upgrade evaluation completed successfully',
    'upgrade_evaluation_failed' => 'Upgrade evaluation failed',

    // Default values
    'default' => 'Default',
    'standard_profile' => 'Standard Profile',
    'percentage_model' => 'Percentage',
    'restaurants' => 'restaurants',

    // Restaurants page
    'restaurants_page' => [
        'page_title' => 'Restaurant Pricing Integration',
        'breadcrumb' => 'Restaurant Integration',
        'title' => 'Restaurant Integration Management',
        'table_title' => 'Restaurant Pricing Integrations',
        'table_subtitle' => 'Manage restaurant connections to unified pricing system',
    ],

    // Statistics
    'stats' => [
        'total_restaurants' => 'Total Restaurants',
        'active_integrations' => 'Active Integrations',
        'pending_integration' => 'Pending Integration',
        'integration_rate' => 'Integration Rate',
    ],

    // Filters
    'filters' => [
        'title' => 'Search and Filter',
        'subtitle' => 'Search restaurants and filter results',
        'status' => 'Integration Status',
        'search_restaurant' => 'Search Restaurant',
        'search_placeholder' => 'Search by restaurant name...',
        'all_statuses' => 'All Statuses',
        'profile' => 'Commission Profile',
        'all_profiles' => 'All Profiles',
    ],

    // Table headers
    'table' => [
        'restaurant' => 'Restaurant',
        'commission_profile' => 'Commission Profile',
        'commission_rates' => 'Commission Rates',
        'orders_processed' => 'Orders Processed',
        'total_commission' => 'Total Commission',
        'status' => 'Status',
        'last_updated' => 'Last Updated',
        'actions' => 'Actions',
    ],

    // Additional messages
    'auto_assign_all' => 'Auto Assign All',
    'no_restaurant_integrations' => 'No Restaurant Integrations',
    'no_restaurant_integrations_desc' => 'No restaurants have been connected to the pricing system yet.',
    'auto_assign_profiles_to_all' => 'Auto Assign Profiles to All Restaurants',
    'auto_assign_complete' => 'Auto Assignment Complete!',
    'successfully_assigned' => 'Successfully assigned:',
    'restaurants_count' => 'restaurants',
    'no_restaurants_to_assign' => 'No restaurants to assign',
    'search_coming_soon' => 'Search Coming Soon',
    'search_functionality_coming_soon' => 'Search functionality will be available soon',
    'refresh' => 'Refresh',
    'confirm_auto_assign' => 'Confirm Auto Assignment',
    'auto_assign_warning' => 'Appropriate commission profiles will be automatically assigned to all restaurants. Do you want to continue?',
    'yes_assign' => 'Yes, Assign',
    'processing' => 'Processing...',
    'please_wait' => 'Please wait...',

    // Types
    'types' => [
        'starter' => 'Starter',
        'standard' => 'Standard',
        'premium' => 'Premium',
        'enterprise' => 'Enterprise',
    ],

    // Status
    'status' => [
        'pending' => 'Pending',
        'suspended' => 'Suspended',
        'active' => 'Active',
    ],

    // Dashboard
    'dashboard' => [
        'title' => 'Unified Pricing Dashboard',
        'refresh' => 'Refresh Dashboard',
        'auto_assign' => 'Auto Assign Profiles',
    ],

    // Profiles
    'profiles' => [
        'title' => 'Commission Profiles',
        'subtitle' => 'Manage commission profiles and pricing models',
    ],

    // Integrations
    'integrations' => [
        'title' => 'Restaurant Integrations',
        'subtitle' => 'Recent restaurant pricing integrations',
    ],

    // Quick Actions
    'quick_actions' => [
        'title' => 'Quick Actions',
        'subtitle' => 'Common tasks and operations',
    ],

    // Actions
    'actions' => [
        'manage_profiles' => 'Manage Profiles',
        'restaurant_integration' => 'Restaurant Integration',
        'evaluate_upgrades' => 'Evaluate Upgrades',
        'analytics' => 'Analytics',
    ],

    // Additional status and messages
    'active_status' => 'Active',
    'inactive_status' => 'Inactive',
    'no_profiles' => 'No Profiles Available',
    'no_profiles_desc' => 'No commission profiles have been created yet.',
    'create_first_profile' => 'Create First Profile',
    'no_integrations' => 'No Integrations',
    'no_integrations_desc' => 'No restaurant integrations have been set up yet.',
    'auto_assign_profiles' => 'Auto Assign Profiles',
    'view_all' => 'View All',
    'loading' => 'Loading...',
    'processing' => 'Processing...',
];
