@extends('layouts.app')

@section('title', 'إدارة المطاعم والتسعير')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">إدارة المطاعم والتسعير</h1>
                    <p class="text-muted">إدارة ملفات التسعير والعمولة المخصصة لكل مطعم</p>
                </div>
                <div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createProfileModal">
                        <i class="fas fa-plus me-2"></i>إنشاء ملف مخصص
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="totalRestaurants">0</h4>
                            <p class="card-text">إجمالي المطاعم</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-store fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="activeProfiles">0</h4>
                            <p class="card-text">الملفات النشطة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="customProfiles">0</h4>
                            <p class="card-text">الملفات المخصصة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-cog fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title" id="standardProfiles">0</h4>
                            <p class="card-text">الملفات القياسية</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-layer-group fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Restaurants Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">قائمة المطاعم</h5>
                </div>
                <div class="card-body">
                    <!-- Search and Filter -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="searchRestaurants" placeholder="البحث في المطاعم...">
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterProfileType">
                                <option value="">جميع أنواع الملفات</option>
                                <option value="standard">قياسي</option>
                                <option value="premium">مميز</option>
                                <option value="custom">مخصص</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="filterStatus">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                                <i class="fas fa-undo me-1"></i>إعادة تعيين
                            </button>
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="restaurantsTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم المطعم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>نوع الملف</th>
                                    <th>معدلات العمولة</th>
                                    <th>رسوم التوصيل</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="restaurantsTableBody">
                                <!-- Data will be loaded via JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Loading Spinner -->
                    <div class="text-center py-4" id="loadingSpinner" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">{{ trans('unified_pricing.loading') }}</span>
                        </div>
                    </div>

                    <!-- No Data Message -->
                    <div class="text-center py-4" id="noDataMessage" style="display: none;">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">{{ trans('unified_pricing.no_restaurants_found') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Profile Modal -->
<div class="modal fade" id="createProfileModal" tabindex="-1" aria-labelledby="createProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createProfileModalLabel">{{ trans('unified_pricing.create_custom_profile') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createProfileForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="restaurant_id" class="form-label">{{ trans('unified_pricing.restaurant') }} <span class="text-danger">*</span></label>
                                <select class="form-select" id="restaurant_id" name="restaurant_id" required>
                                    <option value="">{{ trans('unified_pricing.select_restaurant') }}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="profile_name" class="form-label">{{ trans('unified_pricing.profile_name') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="profile_name" name="profile_name" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">{{ trans('unified_pricing.description') }}</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="custom_admin_commission_rate" class="form-label">{{ trans('unified_pricing.admin_commission_rate') }} (%)</label>
                                <input type="number" class="form-control" id="custom_admin_commission_rate" name="custom_admin_commission_rate" min="0" max="100" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="custom_driver_commission_rate" class="form-label">{{ trans('unified_pricing.driver_commission_rate') }} (%)</label>
                                <input type="number" class="form-control" id="custom_driver_commission_rate" name="custom_driver_commission_rate" min="0" max="100" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="custom_delivery_base_charge" class="form-label">{{ trans('unified_pricing.delivery_base_charge') }} ({{ trans('unified_pricing.currency') }})</label>
                                <input type="number" class="form-control" id="custom_delivery_base_charge" name="custom_delivery_base_charge" min="0" step="0.01">
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {{ trans('unified_pricing.custom_rates_note') }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ trans('unified_pricing.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>{{ trans('unified_pricing.create_profile') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1" aria-labelledby="editProfileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProfileModalLabel">{{ trans('unified_pricing.edit_profile') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editProfileForm">
                <input type="hidden" id="edit_profile_id" name="profile_id">
                <div class="modal-body">
                    <!-- Same form fields as create modal -->
                    <div class="mb-3">
                        <label for="edit_profile_name" class="form-label">{{ trans('unified_pricing.profile_name') }} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_profile_name" name="profile_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">{{ trans('unified_pricing.description') }}</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_custom_admin_commission_rate" class="form-label">{{ trans('unified_pricing.admin_commission_rate') }} (%)</label>
                                <input type="number" class="form-control" id="edit_custom_admin_commission_rate" name="custom_admin_commission_rate" min="0" max="100" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_custom_driver_commission_rate" class="form-label">{{ trans('unified_pricing.driver_commission_rate') }} (%)</label>
                                <input type="number" class="form-control" id="edit_custom_driver_commission_rate" name="custom_driver_commission_rate" min="0" max="100" step="0.01">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_custom_delivery_base_charge" class="form-label">{{ trans('unified_pricing.delivery_base_charge') }} ({{ trans('unified_pricing.currency') }})</label>
                                <input type="number" class="form-control" id="edit_custom_delivery_base_charge" name="custom_delivery_base_charge" min="0" step="0.01">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                            <label class="form-check-label" for="edit_is_active">
                                {{ trans('unified_pricing.profile_active') }}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ trans('unified_pricing.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>{{ trans('unified_pricing.update_profile') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Pricing Calculator Modal -->
<div class="modal fade" id="calculatorModal" tabindex="-1" aria-labelledby="calculatorModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="calculatorModalLabel">{{ trans('unified_pricing.pricing_calculator') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="calculatorForm">
                    <input type="hidden" id="calc_restaurant_id" name="restaurant_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="order_total" class="form-label">{{ trans('unified_pricing.order_total') }} ({{ trans('unified_pricing.currency') }}) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="order_total" name="order_total" min="0" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="distance_km" class="form-label">{{ trans('unified_pricing.distance_km') }}</label>
                                <input type="number" class="form-control" id="distance_km" name="distance_km" min="0" step="0.1">
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-calculator me-2"></i>{{ trans('unified_pricing.calculate') }}
                        </button>
                    </div>
                </form>

                <div id="calculationResults" class="mt-4" style="display: none;">
                    <h6>{{ trans('unified_pricing.calculation_results') }}</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody id="resultsTableBody">
                                <!-- Results will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.badge-custom {
    font-size: 0.75em;
}
.commission-rates {
    font-size: 0.85em;
}
.table th {
    border-top: none;
}
.modal-lg {
    max-width: 800px;
}
</style>
@endpush

@push('scripts')
<script src="{{ asset('js/unified-pricing/restaurant-management.js') }}"></script>
@endpush
