<?php

namespace App\Services;

use App\Models\UnifiedPricingSetting;
use App\Models\RestaurantPricingProfile;
use App\Models\PricingCalculationHistory;
use App\Services\EnhancedDistanceCalculationService;
use App\Services\UnifiedPricingCacheService;
use App\Services\UnifiedPricingPerformanceService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * Unified Pricing Settings Service
 * Manages integration between commission and delivery charge systems
 */
class UnifiedPricingSettingsService
{
    private EnhancedDistanceCalculationService $distanceService;
    private UnifiedPricingCacheService $cacheService;
    private UnifiedPricingPerformanceService $performanceService;
    private const CACHE_TTL = 3600; // 1 hour
    private const CACHE_PREFIX = 'unified_pricing_';

    public function __construct(
        EnhancedDistanceCalculationService $distanceService,
        UnifiedPricingCacheService $cacheService,
        UnifiedPricingPerformanceService $performanceService
    ) {
        $this->distanceService = $distanceService;
        $this->cacheService = $cacheService;
        $this->performanceService = $performanceService;
    }

    /**
     * Create unified pricing setting
     */
    public function createUnifiedSetting(array $data): UnifiedPricingSetting
    {
        DB::beginTransaction();
        
        try {
            $setting = UnifiedPricingSetting::create([
                'id' => Str::uuid(),
                'name' => $data['name'],
                'display_name' => $data['display_name'],
                'description' => $data['description'] ?? null,
                'scope' => $data['scope'],
                'scope_id' => $data['scope_id'] ?? null,
                
                // Distance settings
                'distance_unit' => $data['distance_unit'] ?? 'km',
                'max_delivery_distance' => $data['max_delivery_distance'] ?? 20.00,
                'driver_search_radius' => $data['driver_search_radius'] ?? 5.00,
                
                // Delivery charge settings
                'delivery_charge_type' => $data['delivery_charge_type'],
                'base_delivery_charge' => $data['base_delivery_charge'] ?? 5.00,
                'per_km_delivery_rate' => $data['per_km_delivery_rate'] ?? 1.00,
                'min_delivery_charge' => $data['min_delivery_charge'] ?? 2.00,
                'max_delivery_charge' => $data['max_delivery_charge'] ?? 50.00,
                'free_delivery_above' => $data['free_delivery_above'] ?? null,
                
                // Commission settings
                'commission_calculation_method' => $data['commission_calculation_method'],
                'admin_commission_rate' => $data['admin_commission_rate'] ?? null,
                'driver_commission_rate' => $data['driver_commission_rate'] ?? null,
                'driver_distance_rate' => $data['driver_distance_rate'] ?? null,
                'min_driver_commission' => $data['min_driver_commission'] ?? null,
                'max_driver_commission' => $data['max_driver_commission'] ?? null,
                
                // Time-based settings
                'time_based_multipliers' => $data['time_based_multipliers'] ?? null,
                'day_based_multipliers' => $data['day_based_multipliers'] ?? null,
                'urgent_delivery_multiplier' => $data['urgent_delivery_multiplier'] ?? 1.5,
                
                // Zone settings
                'zone_specific_rates' => $data['zone_specific_rates'] ?? null,
                
                // Status
                'is_active' => $data['is_active'] ?? true,
                'priority' => $data['priority'] ?? 0,
                'effective_from' => $data['effective_from'] ?? now(),
                'effective_until' => $data['effective_until'] ?? null,
                
                // Sync settings
                'auto_sync_enabled' => $data['auto_sync_enabled'] ?? true,
                'metadata' => $data['metadata'] ?? null,
                'created_by' => $data['created_by'] ?? null,
            ]);

            // Create corresponding commission rule and delivery charge setting
            if ($data['auto_sync_enabled'] ?? true) {
                $this->createSyncedSettings($setting);
            }

            DB::commit();
            
            // Clear cache
            $this->clearCache($setting->scope, $setting->scope_id);
            
            Log::info('Unified pricing setting created', ['setting_id' => $setting->id]);
            
            return $setting;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to create unified pricing setting', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Get unified pricing setting by ID
     */
    public function getUnifiedSetting(string $id): ?UnifiedPricingSetting
    {
        try {
            $cacheKey = self::CACHE_PREFIX . "setting_by_id_{$id}";

            return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($id) {
                return UnifiedPricingSetting::with(['calculationHistory', 'commissionRules', 'deliveryChargeSettings'])
                    ->find($id);
            });

        } catch (\Exception $e) {
            Log::error('Failed to get unified setting by ID', [
                'setting_id' => $id,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Update unified pricing setting
     */
    public function updateUnifiedSetting(string $id, array $data): ?UnifiedPricingSetting
    {
        DB::beginTransaction();

        try {
            $setting = UnifiedPricingSetting::findOrFail($id);

            $setting->update(array_filter([
                'name' => $data['name'] ?? null,
                'display_name' => $data['display_name'] ?? null,
                'description' => $data['description'] ?? null,
                'scope' => $data['scope'] ?? null,
                'scope_id' => $data['scope_id'] ?? null,
                'distance_unit' => $data['distance_unit'] ?? null,
                'max_delivery_distance' => $data['max_delivery_distance'] ?? null,
                'driver_search_radius' => $data['driver_search_radius'] ?? null,
                'delivery_charge_type' => $data['delivery_charge_type'] ?? null,
                'base_delivery_charge' => $data['base_delivery_charge'] ?? null,
                'per_km_delivery_rate' => $data['per_km_delivery_rate'] ?? null,
                'min_delivery_charge' => $data['min_delivery_charge'] ?? null,
                'max_delivery_charge' => $data['max_delivery_charge'] ?? null,
                'free_delivery_above' => $data['free_delivery_above'] ?? null,
                'commission_calculation_method' => $data['commission_calculation_method'] ?? null,
                'admin_commission_rate' => $data['admin_commission_rate'] ?? null,
                'driver_commission_rate' => $data['driver_commission_rate'] ?? null,
                'driver_distance_rate' => $data['driver_distance_rate'] ?? null,
                'min_driver_commission' => $data['min_driver_commission'] ?? null,
                'max_driver_commission' => $data['max_driver_commission'] ?? null,
                'time_based_multipliers' => $data['time_based_multipliers'] ?? null,
                'day_based_multipliers' => $data['day_based_multipliers'] ?? null,
                'urgent_delivery_multiplier' => $data['urgent_delivery_multiplier'] ?? null,
                'zone_specific_rates' => $data['zone_specific_rates'] ?? null,
                'is_active' => $data['is_active'] ?? null,
                'priority' => $data['priority'] ?? null,
                'effective_from' => $data['effective_from'] ?? null,
                'effective_until' => $data['effective_until'] ?? null,
                'auto_sync_enabled' => $data['auto_sync_enabled'] ?? null,
                'metadata' => $data['metadata'] ?? null,
                'updated_by' => $data['updated_by'] ?? auth()->id(),
            ], function($value) { return $value !== null; }));

            DB::commit();

            // Clear cache
            $this->clearCache($setting->scope, $setting->scope_id);
            Cache::forget(self::CACHE_PREFIX . "setting_by_id_{$id}");

            Log::info('Unified pricing setting updated', ['setting_id' => $setting->id]);

            return $setting->fresh();

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update unified pricing setting', [
                'setting_id' => $id,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }

    /**
     * Delete unified pricing setting
     */
    public function deleteUnifiedSetting(string $id): bool
    {
        DB::beginTransaction();

        try {
            $setting = UnifiedPricingSetting::findOrFail($id);
            $setting->delete();

            DB::commit();

            // Clear cache
            $this->clearCache($setting->scope, $setting->scope_id);
            Cache::forget(self::CACHE_PREFIX . "setting_by_id_{$id}");

            Log::info('Unified pricing setting deleted', ['setting_id' => $id]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete unified pricing setting', [
                'setting_id' => $id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Calculate comprehensive pricing for an order using restaurant pricing profile
     */
    public function calculateOrderPricing(array $orderData): array
    {
        $startTime = microtime(true);

        try {
            // Get restaurant pricing profile
            $restaurantId = $orderData['restaurant_id'] ?? $orderData['vendor_id'] ?? null;

            if (!$restaurantId) {
                throw new \Exception('Restaurant ID is required for pricing calculation');
            }

            $profile = RestaurantPricingProfile::getActiveProfileForRestaurant($restaurantId);

            if (!$profile) {
                throw new \Exception("No active pricing profile found for restaurant: {$restaurantId}");
            }

            // Calculate distance if coordinates provided
            $distance = null;
            if (isset($orderData['origin_lat'], $orderData['origin_lng'], $orderData['dest_lat'], $orderData['dest_lng'])) {
                $distanceResult = $this->distanceService->calculateDistance(
                    $orderData['origin_lat'],
                    $orderData['origin_lng'],
                    $orderData['dest_lat'],
                    $orderData['dest_lng'],
                    'cached'
                );
                $distance = $distanceResult['distance_km'];
                $orderData['distance_km'] = $distance;
            }

            // Use profile to calculate pricing
            $result = $profile->calculateOrderPricing($orderData);

            $calculationTime = round((microtime(true) - $startTime) * 1000);
            $result['calculation_time_ms'] = $calculationTime;
            $result['distance_km'] = $distance;
            $result['setting_id'] = $profile->unified_pricing_setting_id;

            // Store calculation history if order_id provided
            if (isset($orderData['order_id'])) {
                $this->storeCalculationHistory($orderData['order_id'], $profile->unifiedPricingSetting, $result, $orderData);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('Failed to calculate order pricing', [
                'error' => $e->getMessage(),
                'order_data' => $orderData
            ]);
            throw $e;
        }
    }

    /**
     * Calculate delivery charge based on unified settings
     */
    private function calculateDeliveryCharge(UnifiedPricingSetting $setting, ?float $distance, array $orderData): float
    {
        $orderTotal = $orderData['order_total'] ?? 0;
        $deliveryType = $orderData['delivery_type'] ?? 'normal';

        // Check free delivery threshold
        if ($setting->free_delivery_above && $orderTotal >= $setting->free_delivery_above) {
            return 0;
        }

        // Check distance limit
        if ($distance && $distance > $setting->max_delivery_distance) {
            throw new \Exception('Order exceeds maximum delivery distance');
        }

        $charge = 0;

        switch ($setting->delivery_charge_type) {
            case 'fixed':
                $charge = $setting->base_delivery_charge;
                break;
                
            case 'per_km':
                $charge = $distance ? ($distance * $setting->per_km_delivery_rate) : $setting->base_delivery_charge;
                break;
                
            case 'dynamic':
                $charge = $setting->base_delivery_charge + ($distance ? ($distance * $setting->per_km_delivery_rate) : 0);
                break;
                
            case 'zone_based':
                $charge = $this->calculateZoneBasedCharge($setting, $distance, $orderData);
                break;
        }

        // Apply time-based multipliers
        $charge = $this->applyTimeBasedMultipliers($charge, $setting, $deliveryType);

        // Apply min/max limits
        $charge = max($setting->min_delivery_charge, min($charge, $setting->max_delivery_charge));

        return round($charge, 2);
    }

    /**
     * Calculate driver commission based on unified settings
     */
    private function calculateDriverCommission(UnifiedPricingSetting $setting, ?float $distance, array $orderData, float $deliveryCharge): float
    {
        $orderTotal = $orderData['order_total'] ?? 0;
        $commission = 0;

        switch ($setting->commission_calculation_method) {
            case 'percentage':
                $commission = ($orderTotal * ($setting->driver_commission_rate ?? 0)) / 100;
                break;
                
            case 'fixed':
                $commission = $setting->driver_commission_rate ?? 0;
                break;
                
            case 'distance_based':
                $baseCommission = $setting->driver_commission_rate ?? 0;
                $distanceCommission = $distance ? ($distance * ($setting->driver_distance_rate ?? 0)) : 0;
                $commission = $baseCommission + $distanceCommission;
                break;
                
            case 'hybrid':
                $percentageCommission = ($orderTotal * ($setting->driver_commission_rate ?? 0)) / 100;
                $distanceCommission = $distance ? ($distance * ($setting->driver_distance_rate ?? 0)) : 0;
                $commission = $percentageCommission + $distanceCommission;
                break;
        }

        // Apply min/max limits
        if ($setting->min_driver_commission) {
            $commission = max($commission, $setting->min_driver_commission);
        }
        if ($setting->max_driver_commission) {
            $commission = min($commission, $setting->max_driver_commission);
        }

        return round($commission, 2);
    }

    /**
     * Get applicable unified setting based on scope and priority
     */
    private function getApplicableSetting(string $scope, ?string $scopeId, ?string $zoneId): ?UnifiedPricingSetting
    {
        $cacheKey = self::CACHE_PREFIX . "setting_{$scope}_{$scopeId}_{$zoneId}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($scope, $scopeId, $zoneId) {
            $query = UnifiedPricingSetting::where('is_active', true)
                ->where(function ($q) {
                    $q->whereNull('effective_from')
                      ->orWhere('effective_from', '<=', now());
                })
                ->where(function ($q) {
                    $q->whereNull('effective_until')
                      ->orWhere('effective_until', '>=', now());
                });

            // Try specific scope first
            if ($scopeId) {
                $setting = $query->where('scope', $scope)
                    ->where('scope_id', $scopeId)
                    ->orderBy('priority', 'desc')
                    ->first();
                    
                if ($setting) return $setting;
            }

            // Try zone-specific
            if ($zoneId) {
                $setting = $query->where('scope', 'zone')
                    ->where('scope_id', $zoneId)
                    ->orderBy('priority', 'desc')
                    ->first();
                    
                if ($setting) return $setting;
            }

            // Fallback to global
            return $query->where('scope', 'global')
                ->whereNull('scope_id')
                ->orderBy('priority', 'desc')
                ->first();
        });
    }

    /**
     * Calculate admin commission based on unified settings
     */
    private function calculateAdminCommission(UnifiedPricingSetting $setting, array $orderData, float $deliveryCharge): float
    {
        $orderTotal = $orderData['order_total'] ?? 0;
        $commission = 0;

        if ($setting->admin_commission_rate) {
            $commission = ($orderTotal * $setting->admin_commission_rate) / 100;
        }

        return round($commission, 2);
    }

    /**
     * Apply time-based multipliers
     */
    private function applyTimeBasedMultipliers(float $charge, UnifiedPricingSetting $setting, string $deliveryType): float
    {
        if ($deliveryType === 'urgent') {
            $charge *= $setting->urgent_delivery_multiplier;
        }

        // Apply time-based multipliers
        $timeMultipliers = $setting->time_based_multipliers ?? [];
        $currentHour = now()->format('H:i');

        foreach ($timeMultipliers as $timeRange => $multiplier) {
            if ($this->isTimeInRange($currentHour, $timeRange)) {
                $charge *= $multiplier;
                break;
            }
        }

        // Apply day-based multipliers
        $dayMultipliers = $setting->day_based_multipliers ?? [];
        $currentDay = now()->format('l');

        if (isset($dayMultipliers[$currentDay])) {
            $charge *= $dayMultipliers[$currentDay];
        }

        return $charge;
    }

    /**
     * Check if current time is in range
     */
    private function isTimeInRange(string $currentTime, string $timeRange): bool
    {
        if (!str_contains($timeRange, '-')) {
            return false;
        }

        [$start, $end] = explode('-', $timeRange);

        return $currentTime >= trim($start) && $currentTime <= trim($end);
    }

    /**
     * Calculate zone-based charge
     */
    private function calculateZoneBasedCharge(UnifiedPricingSetting $setting, ?float $distance, array $orderData): float
    {
        $zoneRates = $setting->zone_specific_rates ?? [];

        foreach ($zoneRates as $zone) {
            if ($distance >= ($zone['min_distance'] ?? 0) && $distance <= ($zone['max_distance'] ?? PHP_FLOAT_MAX)) {
                return $zone['rate'] ?? $setting->base_delivery_charge;
            }
        }

        return $setting->base_delivery_charge;
    }

    /**
     * Create synced settings for commission and delivery charge
     */
    private function createSyncedSettings(UnifiedPricingSetting $setting): void
    {
        // This will be implemented to create corresponding settings
        // in the legacy commission_rules and delivery_charge_settings tables
    }

    /**
     * Store calculation history
     */
    private function storeCalculationHistory(string $orderId, UnifiedPricingSetting $setting, array $result, array $orderData): void
    {
        PricingCalculationHistory::storeCalculation($orderId, $setting, $result, $orderData);
    }

    /**
     * Clear cache for specific scope
     */
    private function clearCache(string $scope, ?string $scopeId): void
    {
        $patterns = [
            self::CACHE_PREFIX . "setting_{$scope}_{$scopeId}_*",
            self::CACHE_PREFIX . "setting_global_null_*"
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Create a custom pricing profile for a restaurant
     */
    public function createRestaurantPricingProfile(string $restaurantId, array $profileData): RestaurantPricingProfile
    {
        try {
            // Get or create unified pricing setting for this restaurant
            $unifiedSetting = $this->getOrCreateRestaurantSetting($restaurantId, $profileData);

            // Create the pricing profile
            $profile = RestaurantPricingProfile::create([
                'restaurant_id' => $restaurantId,
                'unified_pricing_setting_id' => $unifiedSetting->id,
                'profile_name' => $profileData['profile_name'] ?? 'ملف مخصص',
                'description' => $profileData['description'] ?? 'ملف تسعير مخصص للمطعم',
                'profile_type' => $profileData['profile_type'] ?? 'custom',
                'custom_admin_commission_rate' => $profileData['custom_admin_commission_rate'] ?? null,
                'custom_driver_commission_rate' => $profileData['custom_driver_commission_rate'] ?? null,
                'custom_delivery_base_charge' => $profileData['custom_delivery_base_charge'] ?? null,
                'is_active' => true,
                'activated_at' => now(),
                'created_by' => auth()->id(),
            ]);

            Log::info('Created restaurant pricing profile', [
                'restaurant_id' => $restaurantId,
                'profile_id' => $profile->id,
                'profile_name' => $profile->profile_name
            ]);

            return $profile;

        } catch (\Exception $e) {
            Log::error('Failed to create restaurant pricing profile', [
                'restaurant_id' => $restaurantId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Update restaurant pricing profile
     */
    public function updateRestaurantPricingProfile(string $profileId, array $updateData): RestaurantPricingProfile
    {
        try {
            $profile = RestaurantPricingProfile::findOrFail($profileId);

            $profile->update(array_merge($updateData, [
                'updated_by' => auth()->id(),
            ]));

            Log::info('Updated restaurant pricing profile', [
                'profile_id' => $profileId,
                'restaurant_id' => $profile->restaurant_id
            ]);

            return $profile->fresh();

        } catch (\Exception $e) {
            Log::error('Failed to update restaurant pricing profile', [
                'profile_id' => $profileId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get or create unified pricing setting for restaurant
     */
    private function getOrCreateRestaurantSetting(string $restaurantId, array $profileData): UnifiedPricingSetting
    {
        // Check if restaurant already has a specific setting
        $existingSetting = UnifiedPricingSetting::forRestaurant($restaurantId)->first();

        if ($existingSetting) {
            return $existingSetting;
        }

        // Get global setting as template
        $globalSetting = UnifiedPricingSetting::global()->active()->first();

        if (!$globalSetting) {
            throw new \Exception('No global pricing setting found to use as template');
        }

        // Create restaurant-specific setting
        $settingData = $globalSetting->toArray();
        unset($settingData['id'], $settingData['created_at'], $settingData['updated_at']);

        return UnifiedPricingSetting::createForRestaurant($restaurantId, array_merge($settingData, [
            'name' => "restaurant_{$restaurantId}_pricing",
            'display_name' => "إعدادات تسعير مخصصة للمطعم",
            'description' => "إعدادات تسعير مخصصة تم إنشاؤها للمطعم",
            // Override with any custom values from profile data
            'admin_commission_rate' => $profileData['admin_commission_rate'] ?? $settingData['admin_commission_rate'],
            'driver_commission_rate' => $profileData['driver_commission_rate'] ?? $settingData['driver_commission_rate'],
            'base_delivery_charge' => $profileData['base_delivery_charge'] ?? $settingData['base_delivery_charge'],
        ]));
    }

    /**
     * Get all restaurants with their pricing profiles
     */
    public function getRestaurantsWithPricingProfiles(): array
    {
        try {
            $restaurants = \App\Models\UnifiedUser::where('role', 'vendor')
                ->where('active', true)
                ->with(['restaurantPricingProfile.unifiedPricingSetting'])
                ->get();

            return $restaurants->map(function ($restaurant) {
                $profile = $restaurant->restaurantPricingProfile;

                return [
                    'restaurant_id' => $restaurant->id,
                    'restaurant_name' => $restaurant->firstName . ' ' . $restaurant->lastName,
                    'restaurant_email' => $restaurant->email,
                    'has_profile' => !is_null($profile),
                    'profile' => $profile ? [
                        'id' => $profile->id,
                        'profile_name' => $profile->profile_name,
                        'profile_type' => $profile->profile_type,
                        'is_active' => $profile->is_active,
                        'has_custom_overrides' => $profile->hasCustomOverrides(),
                        'status' => $profile->getStatusDetails(),
                        'effective_rates' => $profile->getEffectiveCommissionRates(),
                    ] : null
                ];
            })->toArray();

        } catch (\Exception $e) {
            Log::error('Failed to get restaurants with pricing profiles', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
