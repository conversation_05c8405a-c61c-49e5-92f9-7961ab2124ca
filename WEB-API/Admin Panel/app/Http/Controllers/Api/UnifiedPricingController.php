<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\UnifiedPricingSettingsService;
use App\Services\SettingsSynchronizationService;
use App\Models\UnifiedPricingSetting;
use App\Models\RestaurantPricingProfile;
use App\Models\PricingCalculationHistory;
use App\Models\UnifiedUser;
use App\Models\AppUser;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

/**
 * Unified Pricing Controller
 * Manages integrated pricing and commission settings
 */
class UnifiedPricingController extends Controller
{
    private UnifiedPricingSettingsService $pricingService;
    private SettingsSynchronizationService $syncService;

    public function __construct(
        UnifiedPricingSettingsService $pricingService,
        SettingsSynchronizationService $syncService
    ) {
        $this->pricingService = $pricingService;
        $this->syncService = $syncService;
    }

    /**
     * Get all unified pricing settings
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = UnifiedPricingSetting::query();

            // Apply filters
            if ($request->has('scope')) {
                $query->where('scope', $request->scope);
            }

            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            if ($request->has('effective_date')) {
                $query->effective($request->effective_date);
            }

            $settings = $query->orderBy('priority', 'desc')
                ->orderBy('created_at', 'desc')
                ->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'message' => 'تم جلب إعدادات التسعير الموحد بنجاح',
                'data' => $settings
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to fetch unified pricing settings', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب إعدادات التسعير الموحد',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new unified pricing setting
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:unified_pricing_settings,name',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'scope' => 'required|in:global,zone,vendor,driver',
            'scope_id' => 'nullable|uuid',
            'distance_unit' => 'required|in:km,mile',
            'max_delivery_distance' => 'required|numeric|min:1|max:100',
            'driver_search_radius' => 'required|numeric|min:1|max:50',
            'delivery_charge_type' => 'required|in:fixed,per_km,dynamic,zone_based',
            'base_delivery_charge' => 'required|numeric|min:0',
            'per_km_delivery_rate' => 'required|numeric|min:0',
            'min_delivery_charge' => 'required|numeric|min:0',
            'max_delivery_charge' => 'required|numeric|min:0',
            'free_delivery_above' => 'nullable|numeric|min:0',
            'commission_calculation_method' => 'required|in:percentage,fixed,hybrid,distance_based',
            'admin_commission_rate' => 'nullable|numeric|min:0|max:100',
            'driver_commission_rate' => 'nullable|numeric|min:0',
            'driver_distance_rate' => 'nullable|numeric|min:0',
            'min_driver_commission' => 'nullable|numeric|min:0',
            'max_driver_commission' => 'nullable|numeric|min:0',
            'urgent_delivery_multiplier' => 'nullable|numeric|min:1|max:5',
            'time_based_multipliers' => 'nullable|array',
            'day_based_multipliers' => 'nullable|array',
            'zone_specific_rates' => 'nullable|array',
            'is_active' => 'boolean',
            'priority' => 'integer|min:0|max:100',
            'effective_from' => 'nullable|date',
            'effective_until' => 'nullable|date|after:effective_from',
            'auto_sync_enabled' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $data = $validator->validated();
            $data['created_by'] = auth()->id();

            $setting = $this->pricingService->createUnifiedSetting($data);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء إعدادات التسعير الموحد بنجاح',
                'data' => $setting
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create unified pricing setting', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في إنشاء إعدادات التسعير الموحد',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get specific unified pricing setting
     */
    public function show(string $id): JsonResponse
    {
        try {
            $setting = UnifiedPricingSetting::with(['calculationHistory', 'commissionRules', 'deliveryChargeSettings'])
                ->findOrFail($id);

            return response()->json([
                'success' => true,
                'message' => 'تم جلب إعدادات التسعير الموحد بنجاح',
                'data' => $setting
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'إعدادات التسعير الموحد غير موجودة',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update unified pricing setting
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255|unique:unified_pricing_settings,name,' . $id,
            'display_name' => 'sometimes|string|max:255',
            'description' => 'nullable|string',
            'distance_unit' => 'sometimes|in:km,mile',
            'max_delivery_distance' => 'sometimes|numeric|min:1|max:100',
            'driver_search_radius' => 'sometimes|numeric|min:1|max:50',
            'delivery_charge_type' => 'sometimes|in:fixed,per_km,dynamic,zone_based',
            'base_delivery_charge' => 'sometimes|numeric|min:0',
            'per_km_delivery_rate' => 'sometimes|numeric|min:0',
            'min_delivery_charge' => 'sometimes|numeric|min:0',
            'max_delivery_charge' => 'sometimes|numeric|min:0',
            'free_delivery_above' => 'nullable|numeric|min:0',
            'commission_calculation_method' => 'sometimes|in:percentage,fixed,hybrid,distance_based',
            'admin_commission_rate' => 'nullable|numeric|min:0|max:100',
            'driver_commission_rate' => 'nullable|numeric|min:0',
            'driver_distance_rate' => 'nullable|numeric|min:0',
            'min_driver_commission' => 'nullable|numeric|min:0',
            'max_driver_commission' => 'nullable|numeric|min:0',
            'urgent_delivery_multiplier' => 'nullable|numeric|min:1|max:5',
            'time_based_multipliers' => 'nullable|array',
            'day_based_multipliers' => 'nullable|array',
            'zone_specific_rates' => 'nullable|array',
            'is_active' => 'boolean',
            'priority' => 'integer|min:0|max:100',
            'effective_from' => 'nullable|date',
            'effective_until' => 'nullable|date|after:effective_from',
            'auto_sync_enabled' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $setting = UnifiedPricingSetting::findOrFail($id);
            $data = $validator->validated();
            $data['updated_by'] = auth()->id();

            $setting->update($data);

            // Sync with related settings if auto sync is enabled
            if ($setting->auto_sync_enabled) {
                $this->syncService->syncUnifiedSetting($setting);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث إعدادات التسعير الموحد بنجاح',
                'data' => $setting->fresh()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update unified pricing setting', [
                'id' => $id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تحديث إعدادات التسعير الموحد',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete unified pricing setting
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $setting = UnifiedPricingSetting::findOrFail($id);
            $setting->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف إعدادات التسعير الموحد بنجاح'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete unified pricing setting', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في حذف إعدادات التسعير الموحد',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate pricing for an order
     */
    public function calculatePricing(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_total' => 'required|numeric|min:0',
            'origin_lat' => 'required|numeric|between:-90,90',
            'origin_lng' => 'required|numeric|between:-180,180',
            'dest_lat' => 'required|numeric|between:-90,90',
            'dest_lng' => 'required|numeric|between:-180,180',
            'delivery_type' => 'sometimes|in:normal,urgent,scheduled',
            'scope' => 'sometimes|in:global,zone,vendor,driver',
            'scope_id' => 'nullable|uuid',
            'zone_id' => 'nullable|uuid',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $orderData = $validator->validated();
            $result = $this->pricingService->calculateOrderPricing($orderData);

            return response()->json([
                'success' => true,
                'message' => 'تم حساب التسعير بنجاح',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to calculate pricing', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في حساب التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Synchronize all settings
     */
    public function syncAllSettings(): JsonResponse
    {
        try {
            $result = $this->syncService->syncAllSettings();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => $result
            ], $result['success'] ? 200 : 500);

        } catch (\Exception $e) {
            Log::error('Failed to sync all settings', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في مزامنة الإعدادات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Synchronize specific setting
     */
    public function syncSetting(string $id): JsonResponse
    {
        try {
            $setting = UnifiedPricingSetting::findOrFail($id);
            $result = $this->syncService->syncUnifiedSetting($setting);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'تم مزامنة الإعداد بنجاح' : 'فشل في مزامنة الإعداد',
                'data' => $result
            ], $result['success'] ? 200 : 500);

        } catch (\Exception $e) {
            Log::error('Failed to sync setting', ['id' => $id, 'error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في مزامنة الإعداد',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate settings consistency
     */
    public function validateConsistency(): JsonResponse
    {
        try {
            $result = $this->syncService->validateSettingsConsistency();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? 'تم التحقق من تطابق الإعدادات' : 'فشل في التحقق من تطابق الإعدادات',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to validate consistency', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في التحقق من تطابق الإعدادات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get calculation analytics
     */
    public function getAnalytics(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after:start_date',
            'type' => 'sometimes|in:efficiency,breakdown,distance,hourly'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $startDate = $request->start_date;
            $endDate = $request->end_date;
            $type = $request->get('type', 'efficiency');

            $data = match ($type) {
                'efficiency' => PricingCalculationHistory::getEfficiencyMetrics($startDate, $endDate),
                'breakdown' => PricingCalculationHistory::getCalculationBreakdownByMethod($startDate, $endDate),
                'distance' => PricingCalculationHistory::getDistanceBasedAnalysis($startDate, $endDate),
                'hourly' => PricingCalculationHistory::getHourlyPatterns($startDate, $endDate),
                default => PricingCalculationHistory::getEfficiencyMetrics($startDate, $endDate)
            };

            return response()->json([
                'success' => true,
                'message' => 'تم جلب تحليلات التسعير بنجاح',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get analytics', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب تحليلات التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get applicable setting for given parameters
     */
    public function getApplicableSetting(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'scope' => 'sometimes|in:global,zone,vendor,driver',
            'scope_id' => 'nullable|uuid',
            'zone_id' => 'nullable|uuid',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $scope = $request->get('scope', 'global');
            $scopeId = $request->get('scope_id');
            $zoneId = $request->get('zone_id');

            $setting = UnifiedPricingSetting::getApplicableSetting($scope, $scopeId, $zoneId);

            if (!$setting) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا توجد إعدادات تسعير مطبقة للمعايير المحددة'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم جلب الإعدادات المطبقة بنجاح',
                'data' => $setting
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get applicable setting', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب الإعدادات المطبقة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all restaurants with their pricing profiles
     */
    public function getRestaurantsWithProfiles(): JsonResponse
    {
        try {
            $restaurants = $this->pricingService->getRestaurantsWithPricingProfiles();

            return response()->json([
                'success' => true,
                'message' => 'تم جلب المطاعم وملفات التسعير بنجاح',
                'data' => $restaurants
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get restaurants with profiles', ['error' => $e->getMessage()]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب المطاعم وملفات التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create custom pricing profile for restaurant
     */
    public function createRestaurantProfile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'restaurant_id' => 'required|uuid|exists:users,id',
            'profile_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'profile_type' => 'sometimes|in:standard,premium,custom',
            'custom_admin_commission_rate' => 'nullable|numeric|min:0|max:100',
            'custom_driver_commission_rate' => 'nullable|numeric|min:0|max:100',
            'custom_delivery_base_charge' => 'nullable|numeric|min:0',
            'admin_commission_rate' => 'nullable|numeric|min:0|max:100',
            'driver_commission_rate' => 'nullable|numeric|min:0|max:100',
            'base_delivery_charge' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $profile = $this->pricingService->createRestaurantPricingProfile(
                $request->restaurant_id,
                $request->all()
            );

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء ملف التسعير المخصص بنجاح',
                'data' => $profile->load('unifiedPricingSetting')
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create restaurant profile', [
                'restaurant_id' => $request->restaurant_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في إنشاء ملف التسعير المخصص',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update restaurant pricing profile
     */
    public function updateRestaurantProfile(Request $request, string $profileId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'profile_name' => 'sometimes|string|max:255',
            'description' => 'nullable|string|max:1000',
            'custom_admin_commission_rate' => 'nullable|numeric|min:0|max:100',
            'custom_driver_commission_rate' => 'nullable|numeric|min:0|max:100',
            'custom_delivery_base_charge' => 'nullable|numeric|min:0',
            'is_active' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $profile = $this->pricingService->updateRestaurantPricingProfile(
                $profileId,
                $request->all()
            );

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث ملف التسعير بنجاح',
                'data' => $profile->load('unifiedPricingSetting')
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update restaurant profile', [
                'profile_id' => $profileId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في تحديث ملف التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get restaurant pricing profile details
     */
    public function getRestaurantProfile(string $restaurantId): JsonResponse
    {
        try {
            $profile = RestaurantPricingProfile::getActiveProfileForRestaurant($restaurantId);

            if (!$profile) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يوجد ملف تسعير نشط لهذا المطعم'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم جلب ملف التسعير بنجاح',
                'data' => [
                    'profile' => $profile,
                    'unified_setting' => $profile->unifiedPricingSetting,
                    'effective_rates' => $profile->getEffectiveCommissionRates(),
                    'delivery_settings' => $profile->getEffectiveDeliverySettings(),
                    'status' => $profile->getStatusDetails(),
                    'has_custom_overrides' => $profile->hasCustomOverrides(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get restaurant profile', [
                'restaurant_id' => $restaurantId,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب ملف التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate pricing for specific restaurant
     */
    public function calculateRestaurantPricing(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'restaurant_id' => 'required|uuid',
            'order_total' => 'required|numeric|min:0',
            'distance_km' => 'nullable|numeric|min:0',
            'origin_lat' => 'nullable|numeric',
            'origin_lng' => 'nullable|numeric',
            'dest_lat' => 'nullable|numeric',
            'dest_lng' => 'nullable|numeric',
            'is_urgent' => 'sometimes|boolean',
            'order_time' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $result = $this->pricingService->calculateOrderPricing($request->all());

            return response()->json([
                'success' => true,
                'message' => 'تم حساب التسعير بنجاح',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to calculate restaurant pricing', [
                'restaurant_id' => $request->restaurant_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'فشل في حساب التسعير',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
