<?php

namespace App\Http\Controllers;

use App\Models\CommissionProfile;
use App\Models\RestaurantPricingIntegration;
use App\Models\Vendor;
use App\Services\ProfileAssignmentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * Direct Unified Pricing Controller
 * Handles the new direct unified pricing system without templates
 */
class DirectUnifiedPricingController extends Controller
{
    protected $profileAssignmentService;

    public function __construct(ProfileAssignmentService $profileAssignmentService)
    {
        $this->profileAssignmentService = $profileAssignmentService;
    }

    /**
     * Display the main unified pricing dashboard
     */
    public function index()
    {
        try {
            // Get statistics
            $stats = $this->getDashboardStats();
            
            // Get recent integrations
            $recentIntegrations = RestaurantPricingIntegration::with(['restaurant', 'commissionProfile'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            // Get commission profiles
            $profiles = CommissionProfile::active()
                ->withCount('activeRestaurants')
                ->orderBy('profile_type')
                ->get();

            return view('unified-pricing.index', compact('stats', 'recentIntegrations', 'profiles'));

        } catch (\Exception $e) {
            Log::error('Error loading unified pricing dashboard', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return back()->with('error', 'Failed to load dashboard: ' . $e->getMessage());
        }
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats(): array
    {
        return [
            'total_restaurants' => RestaurantPricingIntegration::where('integration_status', 'active')->count(),
            'total_profiles' => CommissionProfile::active()->count(),
            'pending_integrations' => RestaurantPricingIntegration::where('integration_status', 'pending')->count(),
            'upgrade_candidates' => RestaurantPricingIntegration::where('eligible_for_upgrade', true)->count(),
            'total_commission_earned' => RestaurantPricingIntegration::sum('total_commission_earned'),
            'total_orders_processed' => RestaurantPricingIntegration::sum('total_orders_processed'),
        ];
    }

    /**
     * Show commission profiles management
     */
    public function profiles()
    {
        $profiles = CommissionProfile::with(['activeRestaurants'])
            ->withCount('activeRestaurants')
            ->orderBy('profile_type')
            ->paginate(20);

        return view('unified-pricing.profiles.index', compact('profiles'));
    }

    /**
     * Create new commission profile
     */
    public function createProfile()
    {
        return view('unified-pricing.profiles.create');
    }

    /**
     * Store new commission profile
     */
    public function storeProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|unique:commission_profiles,name',
            'display_name' => 'required|string',
            'description' => 'nullable|string',
            'profile_type' => 'required|in:starter,standard,premium,enterprise,promotional,custom',
            'business_model' => 'required|in:percentage,fixed,hybrid,tiered,performance',
            'admin_commission_rate' => 'required|numeric|min:0|max:100',
            'driver_commission_rate' => 'required|numeric|min:0|max:100',
            'platform_fee_rate' => 'nullable|numeric|min:0|max:100',
            'base_delivery_charge' => 'required|numeric|min:0',
            'per_km_delivery_rate' => 'required|numeric|min:0',
            'min_delivery_charge' => 'required|numeric|min:0',
            'max_delivery_charge' => 'required|numeric|min:0',
            'free_delivery_threshold' => 'nullable|numeric|min:0',
            'min_monthly_orders' => 'nullable|integer|min:0',
            'min_rating_required' => 'nullable|numeric|min:0|max:5',
            'auto_assign_eligible' => 'boolean',
            'requires_approval' => 'boolean',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $profile = CommissionProfile::create($request->all());

            Log::info('Commission profile created', [
                'profile_id' => $profile->id,
                'name' => $profile->name,
                'created_by' => auth()->id()
            ]);

            return redirect()->route('unified-pricing.profiles')
                ->with('success', 'Commission profile created successfully!');

        } catch (\Exception $e) {
            Log::error('Failed to create commission profile', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return back()->with('error', 'Failed to create profile: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Show the form for editing a commission profile
     */
    public function editProfile($id)
    {
        try {
            $profile = CommissionProfile::findOrFail($id);

            return view('unified-pricing.profiles.edit', compact('profile', 'id'));

        } catch (\Exception $e) {
            Log::error('Failed to load commission profile for editing', [
                'profile_id' => $id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('unified-pricing.profiles')
                ->with('error', 'Failed to load profile: ' . $e->getMessage());
        }
    }

    /**
     * Update the specified commission profile
     */
    public function updateProfile(Request $request, $id)
    {
        try {
            $profile = CommissionProfile::findOrFail($id);

            // Debug logging
            Log::info('Profile update attempt', [
                'profile_id' => $id,
                'request_data' => $request->all(),
                'user_id' => auth()->id()
            ]);

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|unique:commission_profiles,name,' . $id,
                'display_name' => 'required|string',
                'description' => 'nullable|string',
                'profile_type' => 'required|in:starter,standard,premium,enterprise,promotional,custom',
                'business_model' => 'required|in:percentage,fixed,hybrid,tiered,performance',
                'admin_commission_rate' => 'required|numeric|min:0|max:100',
                'driver_commission_rate' => 'required|numeric|min:0|max:100',
                'platform_fee_rate' => 'nullable|numeric|min:0|max:100',
                'fixed_fee_per_order' => 'nullable|numeric|min:0',
                'base_delivery_charge' => 'required|numeric|min:0',
                'per_km_delivery_rate' => 'required|numeric|min:0',
                'min_delivery_charge' => 'required|numeric|min:0',
                'max_delivery_charge' => 'required|numeric|min:0',
                'free_delivery_threshold' => 'nullable|numeric|min:0',
                'min_monthly_orders' => 'nullable|integer|min:0',
                'min_rating_required' => 'nullable|numeric|min:0|max:5',
                'auto_assign_eligible' => 'boolean',
                'requires_approval' => 'boolean',
                'is_active' => 'boolean'
            ]);

            if ($validator->fails()) {
                Log::warning('Profile update validation failed', [
                    'profile_id' => $id,
                    'errors' => $validator->errors()->toArray(),
                    'request_data' => $request->all()
                ]);
                return back()->withErrors($validator)->withInput();
            }

            // Prepare update data - only include fillable fields
            $updateData = $request->only([
                'name', 'display_name', 'description', 'profile_type', 'business_model',
                'admin_commission_rate', 'driver_commission_rate', 'platform_fee_rate',
                'fixed_fee_per_order', 'payment_processing_fee', 'base_delivery_charge',
                'per_km_delivery_rate', 'min_delivery_charge', 'max_delivery_charge',
                'free_delivery_threshold', 'min_monthly_orders', 'min_rating_required',
                'auto_assign_eligible', 'requires_approval', 'is_active'
            ]);

            // Handle checkboxes (they won't be in request if unchecked)
            $updateData['auto_assign_eligible'] = $request->has('auto_assign_eligible');
            $updateData['requires_approval'] = $request->has('requires_approval');
            $updateData['is_active'] = $request->has('is_active');

            // Update the profile
            $profile->update($updateData);

            // Reload the profile to get fresh data
            $profile->refresh();

            Log::info('Commission profile updated successfully', [
                'profile_id' => $profile->id,
                'name' => $profile->name,
                'updated_fields' => array_keys($updateData),
                'updated_by' => auth()->id()
            ]);

            return redirect()->route('unified-pricing.profiles')
                ->with('success', "Profile '{$profile->display_name}' updated successfully!");

        } catch (\Exception $e) {
            Log::error('Failed to update commission profile', [
                'profile_id' => $id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return back()->with('error', 'Failed to update profile: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified commission profile
     */
    public function destroyProfile($id)
    {
        try {
            $profile = CommissionProfile::findOrFail($id);

            // Check if profile is in use
            if ($profile->activeRestaurants()->count() > 0) {
                return redirect()->route('unified-pricing.profiles')
                    ->with('error', 'Cannot delete profile that is currently assigned to restaurants.');
            }

            $profileName = $profile->name;
            $profile->delete();

            Log::info('Commission profile deleted', [
                'profile_id' => $id,
                'name' => $profileName,
                'deleted_by' => auth()->id()
            ]);

            return redirect()->route('unified-pricing.profiles')
                ->with('success', 'Commission profile deleted successfully!');

        } catch (\Exception $e) {
            Log::error('Failed to delete commission profile', [
                'profile_id' => $id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('unified-pricing.profiles')
                ->with('error', 'Failed to delete profile: ' . $e->getMessage());
        }
    }

    /**
     * Show restaurant integrations
     */
    public function restaurants(Request $request)
    {
        $query = RestaurantPricingIntegration::with(['restaurant', 'commissionProfile']);

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('integration_status', $request->status);
        }

        // Filter by profile
        if ($request->has('profile') && $request->profile !== '') {
            $query->where('commission_profile_id', $request->profile);
        }

        // Search by restaurant name
        if ($request->has('search') && $request->search !== '') {
            $searchTerm = $request->search;
            $query->whereExists(function ($q) use ($searchTerm) {
                $q->select(\DB::raw(1))
                  ->from('vendor_profiles')
                  ->whereRaw('restaurant_pricing_integrations.restaurant_id::uuid = vendor_profiles.id')
                  ->where('vendor_profiles.title', 'ILIKE', "%{$searchTerm}%");
            });
        }

        $integrations = $query->orderBy('created_at', 'desc')->paginate(20);
        $profiles = CommissionProfile::active()->get();

        // Calculate statistics for the restaurants page
        $stats = [
            'total_restaurants' => RestaurantPricingIntegration::distinct('restaurant_id')->count(),
            'active_integrations' => RestaurantPricingIntegration::where('integration_status', 'active')->count(),
            'pending_integration' => RestaurantPricingIntegration::where('integration_status', 'pending')->count(),
            'integration_rate' => RestaurantPricingIntegration::where('integration_status', 'active')->count() > 0
                ? round((RestaurantPricingIntegration::where('integration_status', 'active')->count() / RestaurantPricingIntegration::distinct('restaurant_id')->count()) * 100, 1)
                : 0
        ];

        return view('unified-pricing.restaurants.index', compact('integrations', 'profiles', 'stats'));
    }

    /**
     * Assign profile to restaurant
     */
    public function assignProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'restaurant_id' => 'required|string',
            'profile_id' => 'required|string|exists:commission_profiles,id',
            'reason' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        try {
            // Check if restaurant exists
            $restaurant = Vendor::find($request->restaurant_id);
            if (!$restaurant) {
                return response()->json(['success' => false, 'message' => 'Restaurant not found'], 404);
            }

            // Get or create integration
            $integration = RestaurantPricingIntegration::where('restaurant_id', $request->restaurant_id)
                ->where('integration_status', 'active')
                ->first();

            if ($integration) {
                // Update existing integration
                $integration->changeProfile(
                    $request->profile_id,
                    $request->reason ?? 'Manual assignment by admin',
                    auth()->id()
                );
                $message = 'Profile updated successfully!';
            } else {
                // Create new integration
                $integration = RestaurantPricingIntegration::create([
                    'restaurant_id' => $request->restaurant_id,
                    'commission_profile_id' => $request->profile_id,
                    'integration_status' => 'active',
                    'assignment_method' => 'manual',
                    'assignment_reason' => $request->reason ?? 'Manual assignment by admin',
                    'assigned_by' => auth()->id(),
                    'effective_from' => now(),
                    'auto_update_enabled' => true
                ]);
                $message = 'Profile assigned successfully!';
            }

            Log::info('Profile assigned to restaurant', [
                'restaurant_id' => $request->restaurant_id,
                'profile_id' => $request->profile_id,
                'integration_id' => $integration->id,
                'assigned_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => $message,
                'integration' => $integration->load(['restaurant', 'commissionProfile'])
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to assign profile', [
                'restaurant_id' => $request->restaurant_id,
                'profile_id' => $request->profile_id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to assign profile: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk assign profiles to multiple restaurants
     */
    public function bulkAssignProfiles(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'restaurant_ids' => 'required|array',
            'restaurant_ids.*' => 'string',
            'profile_id' => 'required|string|exists:commission_profiles,id',
            'reason' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'errors' => $validator->errors()], 422);
        }

        try {
            $results = ['successful' => [], 'failed' => []];

            foreach ($request->restaurant_ids as $restaurantId) {
                try {
                    $restaurant = Vendor::find($restaurantId);
                    if (!$restaurant) {
                        $results['failed'][] = [
                            'restaurant_id' => $restaurantId,
                            'error' => 'Restaurant not found'
                        ];
                        continue;
                    }

                    // Get or create integration
                    $integration = RestaurantPricingIntegration::where('restaurant_id', $restaurantId)
                        ->where('integration_status', 'active')
                        ->first();

                    if ($integration) {
                        $integration->changeProfile(
                            $request->profile_id,
                            $request->reason ?? 'Bulk assignment by admin',
                            auth()->id()
                        );
                    } else {
                        $integration = RestaurantPricingIntegration::create([
                            'restaurant_id' => $restaurantId,
                            'commission_profile_id' => $request->profile_id,
                            'integration_status' => 'active',
                            'assignment_method' => 'manual',
                            'assignment_reason' => $request->reason ?? 'Bulk assignment by admin',
                            'assigned_by' => auth()->id(),
                            'effective_from' => now(),
                            'auto_update_enabled' => true
                        ]);
                    }

                    $results['successful'][] = [
                        'restaurant_id' => $restaurantId,
                        'restaurant_name' => $restaurant->name,
                        'integration_id' => $integration->id
                    ];

                } catch (\Exception $e) {
                    $results['failed'][] = [
                        'restaurant_id' => $restaurantId,
                        'error' => $e->getMessage()
                    ];
                }
            }

            Log::info('Bulk profile assignment completed', [
                'profile_id' => $request->profile_id,
                'successful_count' => count($results['successful']),
                'failed_count' => count($results['failed']),
                'assigned_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Bulk assignment completed',
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Bulk profile assignment failed', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Bulk assignment failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Auto-assign profiles to restaurants without integration
     */
    public function autoAssignProfiles()
    {
        try {
            // Get restaurants without active integration
            $restaurantsWithoutIntegration = Vendor::whereDoesntHave('pricingIntegration', function ($query) {
                $query->where('integration_status', 'active');
            })->pluck('id')->toArray();

            if (empty($restaurantsWithoutIntegration)) {
                return response()->json([
                    'success' => true,
                    'message' => 'All restaurants already have pricing integration',
                    'results' => ['successful' => [], 'failed' => []]
                ]);
            }

            $results = $this->profileAssignmentService->bulkAssignProfiles($restaurantsWithoutIntegration);

            Log::info('Auto-assignment completed', [
                'total_restaurants' => count($restaurantsWithoutIntegration),
                'successful_count' => count($results['successful']),
                'failed_count' => count($results['failed']),
                'triggered_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Auto-assignment completed successfully',
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Auto-assignment failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Auto-assignment failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show analytics page
     */
    public function analytics()
    {
        // TODO: Implement analytics functionality
        return view('unified-pricing.analytics');
    }

    /**
     * Show reports page
     */
    public function reports()
    {
        // TODO: Implement reports functionality
        return view('unified-pricing.reports');
    }

    /**
     * Evaluate restaurants for profile upgrades
     */
    public function evaluateUpgrades()
    {
        try {
            $results = $this->profileAssignmentService->evaluateUpgrades();

            Log::info('Profile upgrade evaluation completed', [
                'results' => $results,
                'triggered_by' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Upgrade evaluation completed successfully',
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('Profile upgrade evaluation failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Upgrade evaluation failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
