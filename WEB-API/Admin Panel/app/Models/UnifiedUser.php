<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

/**
 * النموذج الموحد للمستخدمين - يحل محل النماذج المتعددة
 * Unified User Model - Replaces Multiple User Models
 */
class UnifiedUser extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $connection = 'pgsql';
    protected $table = 'users';
    protected $keyType = 'string';
    public $incrementing = false;

    // الأدوار المتاحة
    const ROLE_CUSTOMER = 'customer';
    const ROLE_VENDOR = 'vendor';
    const ROLE_DRIVER = 'driver';
    const ROLE_ADMIN = 'admin';

    protected $fillable = [
        'id',
        'firstName',
        'lastName',
        'email',
        'phoneNumber',
        'countryCode',
        'profilePictureURL',
        'role',
        'user_type',
        'active',
        'isDocumentVerify',
        'fcmToken',
        'wallet_amount',
        'zoneId',
        'lastOnlineTimestamp',
        'password',
        'remember_token',
    ];

    // تحديد أسماء الأعمدة الصحيحة لـ timestamps
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';

    protected $hidden = [
        'password',
        'remember_token',
        'fcmToken',
    ];

    protected $casts = [
        'active' => 'boolean',
        'isDocumentVerify' => 'boolean',
        'wallet_amount' => 'decimal:2',
        'lastOnlineTimestamp' => 'datetime',
        'email_verified_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot method to set default values
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($user) {
            if (empty($user->role)) {
                $user->role = self::ROLE_CUSTOMER;
            }
            if (empty($user->user_type)) {
                $user->user_type = $user->role;
            }
        });
    }

    /**
     * Get the user's full name
     */
    public function getFullNameAttribute()
    {
        return trim($this->firstName . ' ' . $this->lastName);
    }

    /**
     * Check if user is a customer
     */
    public function isCustomer()
    {
        return $this->role === self::ROLE_CUSTOMER;
    }

    /**
     * Check if user is a vendor
     */
    public function isVendor()
    {
        return $this->role === self::ROLE_VENDOR;
    }

    /**
     * Check if user is a driver
     */
    public function isDriver()
    {
        return $this->role === self::ROLE_DRIVER;
    }

    /**
     * Check if user is an admin
     */
    public function isAdmin()
    {
        return $this->role === self::ROLE_ADMIN;
    }

    /**
     * Relationship: Customer Profile (one-to-one)
     */
    public function customerProfile()
    {
        return $this->hasOne(CustomerProfile::class, 'user_id', 'id');
    }

    /**
     * Relationship: Vendor Profile (one-to-one)
     */
    public function vendorProfile()
    {
        return $this->hasOne(VendorProfile::class, 'user_id', 'id');
    }

    /**
     * Relationship: Driver Profile (one-to-one)
     */
    public function driverProfile()
    {
        return $this->hasOne(DriverProfile::class, 'user_id', 'id');
    }

    /**
     * Relationship: Orders as Customer (one-to-many)
     */
    public function orders()
    {
        return $this->hasMany(Order::class, 'authorID', 'id');
    }

    /**
     * Relationship: Orders as Vendor (one-to-many)
     */
    public function vendorOrders()
    {
        return $this->hasMany(Order::class, 'vendorID', 'id');
    }

    /**
     * Relationship: Orders as Driver (one-to-many)
     */
    public function driverOrders()
    {
        return $this->hasMany(Order::class, 'driverID', 'id');
    }

 

    /**
     * Relationship: Wallet Transactions (one-to-many)
     */
    public function walletTransactions()
    {
        return $this->hasMany(WalletTransaction::class, 'user_id', 'id');
    }

    /**
     * Dynamic Profile Accessor - Returns appropriate profile based on role
     */
    public function getProfileAttribute()
    {
        switch ($this->role) {
            case self::ROLE_CUSTOMER:
                return $this->customerProfile;
            case self::ROLE_VENDOR:
                return $this->vendorProfile;
            case self::ROLE_DRIVER:
                return $this->driverProfile;
            default:
                return null;
        }
    }

    /**
     * Get profile with automatic creation if not exists
     */
    public function getOrCreateProfile()
    {
        $profile = $this->profile;

        if (!$profile) {
            switch ($this->role) {
                case self::ROLE_CUSTOMER:
                    $profile = $this->customerProfile()->create([
                        'user_id' => $this->id,
                        'loyaltypoints' => 0,
                        'totalorders' => 0,
                        'totalspent' => 0.00,
                        'averageordervalue' => 0.00,
                    ]);
                    break;
                case self::ROLE_VENDOR:
                    $profile = $this->vendorProfile()->create([
                        'id' => \Illuminate\Support\Str::uuid(),
                        'user_id' => $this->id,
                        'title' => $this->firstName . ' ' . $this->lastName,
                        'reviewsCount' => 0,
                        'reviewsSum' => 0.00,
                        'averageRating' => 0.00,
                        'restStatus' => true,
                        'isVerified' => false,
                        'workingHours' => [],
                        'deliveryRadius' => 5.00,
                        'minimumOrder' => 0.00,
                        'deliveryFee' => 0.00,
                        'estimatedDeliveryTime' => 30,
                        'address' => [],
                        'settings' => [],
                        'specialDiscountEnable' => false,
                        'enabledDiveInFuture' => false,
                    ]);
                    break;
                case self::ROLE_DRIVER:
                    $profile = $this->driverProfile()->create([
                        'user_id' => $this->id,
                        'isavailable' => true,
                        'totaldeliveries' => 0,
                        'rating' => 5.0,
                    ]);
                    break;
            }
        }

        return $profile;
    }



    /**
     * Relationship: Zone
     */
    public function zone()
    {
        return $this->belongsTo(Zone::class, 'zoneId');
    }

    /**
     * Relationship: Restaurant Pricing Profile (for vendors)
     */
    public function restaurantPricingProfile()
    {
        return $this->hasOne(RestaurantPricingProfile::class, 'restaurant_id');
    }





    /**
     * Relationship: Payments
     */
    public function payments()
    {
        return $this->hasMany(Payment::class, 'user_id');
    }

    /**
     * Scopes for filtering by role
     */
    public function scopeCustomers($query)
    {
        return $query->where('role', self::ROLE_CUSTOMER);
    }

    public function scopeVendors($query)
    {
        return $query->where('role', self::ROLE_VENDOR);
    }

    public function scopeDrivers($query)
    {
        return $query->where('role', self::ROLE_DRIVER);
    }

    public function scopeAdmins($query)
    {
        return $query->where('role', self::ROLE_ADMIN);
    }

    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    public function scopeVerified($query)
    {
        return $query->where('isDocumentVerify', true);
    }



    /**
     * Wallet operations
     */
    public function addToWallet($amount, $description = null)
    {
        $this->increment('wallet_amount', $amount);
        
        // Log wallet transaction if WalletTransaction model exists
        if (class_exists('App\Models\WalletTransaction')) {
            WalletTransaction::create([
                'user_id' => $this->id,
                'type' => 'credit',
                'amount' => $amount,
                'description' => $description,
                'balance_after' => $this->fresh()->wallet_amount,
            ]);
        }
        
        return $this;
    }

    public function deductFromWallet($amount, $description = null)
    {
        if ($this->wallet_amount < $amount) {
            throw new \Exception('Insufficient wallet balance');
        }
        
        $this->decrement('wallet_amount', $amount);
        
        // Log wallet transaction if WalletTransaction model exists
        if (class_exists('App\Models\WalletTransaction')) {
            WalletTransaction::create([
                'user_id' => $this->id,
                'type' => 'debit',
                'amount' => $amount,
                'description' => $description,
                'balance_after' => $this->fresh()->wallet_amount,
            ]);
        }
        
        return $this;
    }

    /**
     * Statistics methods
     */
    public function getTotalOrdersAttribute()
    {
        switch ($this->role) {
            case self::ROLE_CUSTOMER:
                return $this->orders()->count();
            case self::ROLE_VENDOR:
                return $this->vendorOrders()->count();
            case self::ROLE_DRIVER:
                return $this->driverOrders()->count();
            default:
                return 0;
        }
    }

    public function getTotalSpentAttribute()
    {
        if ($this->role === self::ROLE_CUSTOMER) {
            return $this->orders()->sum('total');
        }
        return 0;
    }

    public function getTotalEarningsAttribute()
    {
        if ($this->role === self::ROLE_VENDOR) {
            return $this->vendorOrders()->sum('total');
        } elseif ($this->role === self::ROLE_DRIVER) {
            return $this->driverOrders()->sum('deliveryCharge');
        }
        return 0;
    }

    /**
     * Validation Rules
     */
    public static function validationRules($role = null): array
    {
        $rules = [
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phoneNumber' => 'required|string|max:20',
            'countryCode' => 'required|string|max:10',
            'role' => 'required|in:customer,vendor,driver,admin',
            'zoneId' => 'nullable|exists:zones,id',
        ];

        // Add role-specific rules
        if ($role === self::ROLE_VENDOR) {
            $rules['businessName'] = 'required|string|max:255';
            $rules['businessType'] = 'required|string|max:100';
        } elseif ($role === self::ROLE_DRIVER) {
            $rules['vehicleType'] = 'required|string|max:100';
            $rules['licenseNumber'] = 'required|string|max:100';
        }

        return $rules;
    }
}
