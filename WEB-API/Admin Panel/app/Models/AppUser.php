<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class AppUser extends Authenticatable
{
    use HasFactory, Notifiable;

    protected $connection = 'pgsql'; // Use PostgreSQL connection
    protected $table = 'users';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'firstName',
        'lastName', 
        'email',
        'phoneNumber',
        'profilePictureURL',
        'fcmToken',
        'wallet_amount',
        'user_type',
        'active',
        'role',
        'createdAt',
        'lastOnlineTimestamp',
        'settings'
    ];

    protected $casts = [
        'active' => 'boolean',
        'wallet_amount' => 'decimal:2',
        'createdAt' => 'datetime',
        'lastOnlineTimestamp' => 'datetime',
        'settings' => 'array'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the user's full name
     */
    public function getFullNameAttribute()
    {
        return $this->firstName . ' ' . $this->lastName;
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope for users by role
     */
    public function scopeByRole($query, $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Check if user is customer
     */
    public function isCustomer()
    {
        return $this->role === 'customer';
    }

    /**
     * Check if user is driver
     */
    public function isDriver()
    {
        return $this->role === 'driver';
    }

    /**
     * Check if user is vendor
     */
    public function isVendor()
    {
        return $this->role === 'vendor';
    }

    /**
     * Orders as customer
     */
    public function customerOrders()
    {
        return $this->hasMany(Order::class, 'authorID');
    }

    /**
     * Orders as driver
     */
    public function driverOrders()
    {
        return $this->hasMany(Order::class, 'driverID');
    }

    /**
     * Vendor profile relationship
     */
    public function vendorProfile()
    {
        return $this->hasOne(Vendor::class, 'user_id', 'id');
    }

    /**
     * Zone relationship
     */
    public function zone()
    {
        return $this->belongsTo(Zone::class, 'zoneId', 'id');
    }

    /**
     * Orders as vendor
     */
    public function vendorOrders()
    {
        return $this->hasMany(Order::class, 'vendorID');
    }

    /**
     * Restaurant Pricing Profile relationship (for vendors)
     */
    public function restaurantPricingProfile()
    {
        return $this->hasOne(RestaurantPricingProfile::class, 'restaurant_id');
    }

    /**
     * Wallet transactions
     */
    public function walletTransactions()
    {
        return $this->hasMany(WalletTransaction::class, 'user_id');
    }
}
