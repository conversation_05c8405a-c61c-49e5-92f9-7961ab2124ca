<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

/**
 * Unified Pricing Setting Model
 * Manages integrated pricing and commission settings
 */
class UnifiedPricingSetting extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';
    protected $table = 'unified_pricing_settings';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'name',
        'display_name',
        'description',
        'scope',
        'scope_id',
        'distance_unit',
        'max_delivery_distance',
        'driver_search_radius',
        'delivery_charge_type',
        'base_delivery_charge',
        'per_km_delivery_rate',
        'min_delivery_charge',
        'max_delivery_charge',
        'free_delivery_above',
        'commission_calculation_method',
        'admin_commission_rate',
        'driver_commission_rate',
        'driver_distance_rate',
        'min_driver_commission',
        'max_driver_commission',
        'time_based_multipliers',
        'day_based_multipliers',
        'urgent_delivery_multiplier',
        'zone_specific_rates',
        'is_active',
        'priority',
        'effective_from',
        'effective_until',
        'auto_sync_enabled',
        'last_synced_at',
        'sync_log',
        'metadata',
        'created_by',
        'updated_by',
        // New fields for direct restaurant integration
        'is_restaurant_specific',
        'target_restaurant_id',
        'converted_from_template',
        'original_template_id',
    ];

    protected $casts = [
        'max_delivery_distance' => 'decimal:2',
        'driver_search_radius' => 'decimal:2',
        'base_delivery_charge' => 'decimal:2',
        'per_km_delivery_rate' => 'decimal:2',
        'min_delivery_charge' => 'decimal:2',
        'max_delivery_charge' => 'decimal:2',
        'free_delivery_above' => 'decimal:2',
        'admin_commission_rate' => 'decimal:2',
        'driver_commission_rate' => 'decimal:2',
        'driver_distance_rate' => 'decimal:2',
        'min_driver_commission' => 'decimal:2',
        'max_driver_commission' => 'decimal:2',
        'urgent_delivery_multiplier' => 'decimal:2',
        'time_based_multipliers' => 'array',
        'day_based_multipliers' => 'array',
        'zone_specific_rates' => 'array',
        'is_active' => 'boolean',
        'priority' => 'integer',
        'effective_from' => 'datetime',
        'effective_until' => 'datetime',
        'auto_sync_enabled' => 'boolean',
        'last_synced_at' => 'datetime',
        'sync_log' => 'array',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        // New casts for direct restaurant integration
        'is_restaurant_specific' => 'boolean',
        'converted_from_template' => 'boolean',
    ];

    /**
     * Boot method to generate UUID
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
        });

        static::updating(function ($model) {
            $model->updated_by = auth()->id();
        });
    }

    /**
     * Get calculation history
     */
    public function calculationHistory(): HasMany
    {
        return $this->hasMany(PricingCalculationHistory::class);
    }

    /**
     * Get related commission rules
     */
    public function commissionRules(): HasMany
    {
        return $this->hasMany(CommissionRule::class);
    }

    /**
     * Get related delivery charge settings
     */
    public function deliveryChargeSettings(): HasMany
    {
        return $this->hasMany(DeliveryChargeSetting::class);
    }

    /**
     * Get the creator
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the updater
     */
    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope for active settings
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for effective settings
     */
    public function scopeEffective($query, $date = null)
    {
        $date = $date ?: now();
        
        return $query->where(function ($q) use ($date) {
            $q->whereNull('effective_from')
              ->orWhere('effective_from', '<=', $date);
        })->where(function ($q) use ($date) {
            $q->whereNull('effective_until')
              ->orWhere('effective_until', '>=', $date);
        });
    }

    /**
     * Scope for specific scope
     */
    public function scopeForScope($query, string $scope, ?string $scopeId = null)
    {
        $query->where('scope', $scope);
        
        if ($scopeId) {
            $query->where('scope_id', $scopeId);
        } else {
            $query->whereNull('scope_id');
        }
        
        return $query;
    }

    /**
     * Get applicable setting for given parameters
     */
    public static function getApplicableSetting(string $scope = 'global', ?string $scopeId = null, ?string $zoneId = null): ?self
    {
        // Try specific scope first
        if ($scopeId) {
            $setting = static::active()
                ->effective()
                ->forScope($scope, $scopeId)
                ->orderBy('priority', 'desc')
                ->first();
                
            if ($setting) return $setting;
        }

        // Try zone-specific
        if ($zoneId) {
            $setting = static::active()
                ->effective()
                ->forScope('zone', $zoneId)
                ->orderBy('priority', 'desc')
                ->first();
                
            if ($setting) return $setting;
        }

        // Fallback to global
        return static::active()
            ->effective()
            ->forScope('global')
            ->orderBy('priority', 'desc')
            ->first();
    }

    /**
     * Calculate delivery charge
     */
    public function calculateDeliveryCharge(float $distance, float $orderTotal, string $deliveryType = 'normal'): float
    {
        // Check free delivery threshold
        if ($this->free_delivery_above && $orderTotal >= $this->free_delivery_above) {
            return 0;
        }

        // Check distance limit
        if ($distance > $this->max_delivery_distance) {
            throw new \Exception('Distance exceeds maximum delivery distance');
        }

        $charge = 0;

        switch ($this->delivery_charge_type) {
            case 'fixed':
                $charge = $this->base_delivery_charge;
                break;
                
            case 'per_km':
                $charge = $distance * $this->per_km_delivery_rate;
                break;
                
            case 'dynamic':
                $charge = $this->base_delivery_charge + ($distance * $this->per_km_delivery_rate);
                break;
                
            case 'zone_based':
                $charge = $this->calculateZoneBasedCharge($distance);
                break;
        }

        // Apply time-based multipliers
        $charge = $this->applyTimeMultipliers($charge, $deliveryType);

        // Apply min/max limits
        $charge = max($this->min_delivery_charge, min($charge, $this->max_delivery_charge));

        return round($charge, 2);
    }

    /**
     * Calculate driver commission
     */
    public function calculateDriverCommission(float $distance, float $orderTotal): float
    {
        $commission = 0;

        switch ($this->commission_calculation_method) {
            case 'percentage':
                $commission = ($orderTotal * $this->driver_commission_rate) / 100;
                break;
                
            case 'fixed':
                $commission = $this->driver_commission_rate;
                break;
                
            case 'distance_based':
                $commission = ($this->driver_commission_rate ?? 0) + ($distance * ($this->driver_distance_rate ?? 0));
                break;
                
            case 'hybrid':
                $percentageCommission = ($orderTotal * ($this->driver_commission_rate ?? 0)) / 100;
                $distanceCommission = $distance * ($this->driver_distance_rate ?? 0);
                $commission = $percentageCommission + $distanceCommission;
                break;
        }

        // Apply min/max limits
        if ($this->min_driver_commission) {
            $commission = max($commission, $this->min_driver_commission);
        }
        if ($this->max_driver_commission) {
            $commission = min($commission, $this->max_driver_commission);
        }

        return round($commission, 2);
    }

    /**
     * Calculate zone-based charge
     */
    private function calculateZoneBasedCharge(float $distance): float
    {
        $zoneRates = $this->zone_specific_rates ?? [];
        
        foreach ($zoneRates as $zone) {
            if ($distance >= ($zone['min_distance'] ?? 0) && $distance <= ($zone['max_distance'] ?? PHP_FLOAT_MAX)) {
                return $zone['rate'] ?? $this->base_delivery_charge;
            }
        }
        
        return $this->base_delivery_charge;
    }

    /**
     * Apply time-based multipliers
     */
    private function applyTimeMultipliers(float $charge, string $deliveryType): float
    {
        if ($deliveryType === 'urgent') {
            $charge *= $this->urgent_delivery_multiplier;
        }

        // Apply time-based multipliers
        $timeMultipliers = $this->time_based_multipliers ?? [];
        $currentHour = now()->format('H:i');
        
        foreach ($timeMultipliers as $timeRange => $multiplier) {
            if ($this->isTimeInRange($currentHour, $timeRange)) {
                $charge *= $multiplier;
                break;
            }
        }

        // Apply day-based multipliers
        $dayMultipliers = $this->day_based_multipliers ?? [];
        $currentDay = now()->format('l');
        
        if (isset($dayMultipliers[$currentDay])) {
            $charge *= $dayMultipliers[$currentDay];
        }

        return $charge;
    }

    /**
     * Check if current time is in range
     */
    private function isTimeInRange(string $currentTime, string $timeRange): bool
    {
        if (!str_contains($timeRange, '-')) {
            return false;
        }
        
        [$start, $end] = explode('-', $timeRange);
        
        return $currentTime >= trim($start) && $currentTime <= trim($end);
    }

    /**
     * Sync with related settings
     */
    public function syncWithRelatedSettings(): bool
    {
        if (!$this->auto_sync_enabled) {
            return false;
        }

        try {
            // Sync commission rules
            $this->syncCommissionRules();
            
            // Sync delivery charge settings
            $this->syncDeliveryChargeSettings();
            
            $this->update([
                'last_synced_at' => now(),
                'sync_log' => array_merge($this->sync_log ?? [], [
                    'synced_at' => now()->toISOString(),
                    'status' => 'success'
                ])
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            $this->update([
                'sync_log' => array_merge($this->sync_log ?? [], [
                    'synced_at' => now()->toISOString(),
                    'status' => 'error',
                    'error' => $e->getMessage()
                ])
            ]);
            
            return false;
        }
    }

    /**
     * Sync commission rules
     */
    private function syncCommissionRules(): void
    {
        // Implementation for syncing commission rules
        // This will be implemented based on specific business logic
    }

    /**
     * Sync delivery charge settings
     */
    private function syncDeliveryChargeSettings(): void
    {
        // Implementation for syncing delivery charge settings
        // This will be implemented based on specific business logic
    }

    /**
     * Get restaurant pricing profiles using this setting
     */
    public function restaurantProfiles(): HasMany
    {
        return $this->hasMany(RestaurantPricingProfile::class, 'unified_pricing_setting_id');
    }

    /**
     * Get the target restaurant if this is restaurant-specific
     */
    public function targetRestaurant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'target_restaurant_id');
    }

    /**
     * Scope to get global settings
     */
    public function scopeGlobal($query)
    {
        return $query->where('scope', 'global');
    }

    /**
     * Scope to get restaurant-specific settings
     */
    public function scopeRestaurantSpecific($query)
    {
        return $query->where('is_restaurant_specific', true);
    }

    /**
     * Scope to get settings for a specific restaurant
     */
    public function scopeForRestaurant($query, $restaurantId)
    {
        return $query->where('target_restaurant_id', $restaurantId);
    }

    /**
     * Create a restaurant-specific pricing setting
     */
    public static function createForRestaurant(string $restaurantId, array $settings): self
    {
        return self::create(array_merge($settings, [
            'scope' => 'vendor',
            'scope_id' => $restaurantId,
            'is_restaurant_specific' => true,
            'target_restaurant_id' => $restaurantId,
            'is_active' => true,
            'priority' => 10, // Higher priority than global
            'effective_from' => now(),
        ]));
    }

    /**
     * Get the applicable setting for a restaurant
     */
    public static function getApplicableForRestaurant(string $restaurantId): ?self
    {
        // First try restaurant-specific setting
        $restaurantSpecific = self::where('target_restaurant_id', $restaurantId)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('effective_until')
                      ->orWhere('effective_until', '>', now());
            })
            ->orderBy('priority', 'desc')
            ->first();

        if ($restaurantSpecific) {
            return $restaurantSpecific;
        }

        // Fall back to global setting
        return self::where('scope', 'global')
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('effective_until')
                      ->orWhere('effective_until', '>', now());
            })
            ->orderBy('priority', 'desc')
            ->first();
    }
}
