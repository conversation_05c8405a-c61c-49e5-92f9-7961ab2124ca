<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\UnifiedUser;
use App\Models\UnifiedPricingSetting;
use App\Models\RestaurantPricingProfile;
use App\Services\UnifiedPricingSettingsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Str;

/**
 * Test suite for Unified Pricing Integration
 * Tests the complete integration between pricing and commission systems
 */
class UnifiedPricingIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $pricingService;
    protected $testRestaurant;
    protected $globalPricingSetting;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->pricingService = app(UnifiedPricingSettingsService::class);
        
        // Create test data
        $this->createTestData();
    }

    protected function createTestData()
    {
        // Create a test restaurant
        $this->testRestaurant = UnifiedUser::create([
            'id' => Str::uuid(),
            'firstName' => 'Test',
            'lastName' => 'Restaurant',
            'email' => '<EMAIL>',
            'phoneNumber' => '1234567890',
            'role' => 'vendor',
            'active' => true,
        ]);

        // Create global pricing setting
        $this->globalPricingSetting = UnifiedPricingSetting::create([
            'id' => Str::uuid(),
            'name' => 'test_global_pricing',
            'display_name' => 'Test Global Pricing',
            'description' => 'Test global pricing setting',
            'scope' => 'global',
            'distance_unit' => 'km',
            'max_delivery_distance' => 25.00,
            'delivery_charge_type' => 'dynamic',
            'base_delivery_charge' => 8.00,
            'per_km_delivery_rate' => 1.50,
            'min_delivery_charge' => 6.00,
            'max_delivery_charge' => 60.00,
            'free_delivery_above' => 120.00,
            'commission_calculation_method' => 'percentage',
            'admin_commission_rate' => 12.00,
            'driver_commission_rate' => 8.00,
            'driver_distance_rate' => 0.75,
            'min_driver_commission' => 5.00,
            'max_driver_commission' => 30.00,
            'is_active' => true,
            'priority' => 1,
            'effective_from' => now(),
        ]);
    }

    /** @test */
    public function it_can_create_restaurant_pricing_profile()
    {
        $profileData = [
            'profile_name' => 'Test Custom Profile',
            'description' => 'Test profile description',
            'profile_type' => 'custom',
            'custom_admin_commission_rate' => 15.00,
            'custom_driver_commission_rate' => 10.00,
            'custom_delivery_base_charge' => 10.00,
        ];

        $profile = $this->pricingService->createRestaurantPricingProfile(
            $this->testRestaurant->id,
            $profileData
        );

        $this->assertInstanceOf(RestaurantPricingProfile::class, $profile);
        $this->assertEquals($this->testRestaurant->id, $profile->restaurant_id);
        $this->assertEquals('Test Custom Profile', $profile->profile_name);
        $this->assertEquals('custom', $profile->profile_type);
        $this->assertTrue($profile->is_active);
        $this->assertTrue($profile->hasCustomOverrides());
    }

    /** @test */
    public function it_can_calculate_pricing_with_restaurant_profile()
    {
        // Create a custom profile
        $profile = RestaurantPricingProfile::create([
            'id' => Str::uuid(),
            'restaurant_id' => $this->testRestaurant->id,
            'unified_pricing_setting_id' => $this->globalPricingSetting->id,
            'profile_name' => 'Test Profile',
            'profile_type' => 'custom',
            'custom_admin_commission_rate' => 15.00,
            'custom_driver_commission_rate' => 10.00,
            'custom_delivery_base_charge' => 12.00,
            'is_active' => true,
            'activated_at' => now(),
        ]);

        $orderData = [
            'restaurant_id' => $this->testRestaurant->id,
            'order_total' => 100.00,
            'distance_km' => 5.0,
        ];

        $result = $this->pricingService->calculateOrderPricing($orderData);

        $this->assertIsArray($result);
        $this->assertEquals(100.00, $result['order_total']);
        $this->assertEquals(15.00, $result['admin_commission']); // Custom rate
        $this->assertGreaterThan(0, $result['delivery_charge']);
        $this->assertGreaterThan(0, $result['driver_commission']);
        $this->assertEquals('Test Profile', $result['profile_used']);
    }

    /** @test */
    public function it_uses_global_settings_when_no_custom_overrides()
    {
        // Create a standard profile without custom overrides
        $profile = RestaurantPricingProfile::create([
            'id' => Str::uuid(),
            'restaurant_id' => $this->testRestaurant->id,
            'unified_pricing_setting_id' => $this->globalPricingSetting->id,
            'profile_name' => 'Standard Profile',
            'profile_type' => 'standard',
            'is_active' => true,
            'activated_at' => now(),
        ]);

        $orderData = [
            'restaurant_id' => $this->testRestaurant->id,
            'order_total' => 100.00,
            'distance_km' => 5.0,
        ];

        $result = $this->pricingService->calculateOrderPricing($orderData);

        $this->assertEquals(12.00, $result['admin_commission']); // Global rate
        $this->assertFalse($result['calculation_details']['has_custom_overrides']);
    }

    /** @test */
    public function it_calculates_delivery_charge_correctly()
    {
        $profile = RestaurantPricingProfile::create([
            'id' => Str::uuid(),
            'restaurant_id' => $this->testRestaurant->id,
            'unified_pricing_setting_id' => $this->globalPricingSetting->id,
            'profile_name' => 'Test Profile',
            'profile_type' => 'standard',
            'is_active' => true,
            'activated_at' => now(),
        ]);

        // Test dynamic delivery charge calculation
        $orderData = [
            'restaurant_id' => $this->testRestaurant->id,
            'order_total' => 50.00,
            'distance_km' => 3.0,
        ];

        $result = $this->pricingService->calculateOrderPricing($orderData);

        // Expected: base_charge (8.00) + (distance * per_km_rate) (3.0 * 1.50) = 12.50
        $expectedDeliveryCharge = 8.00 + (3.0 * 1.50);
        $this->assertEquals($expectedDeliveryCharge, $result['delivery_charge']);
    }

    /** @test */
    public function it_applies_free_delivery_threshold()
    {
        $profile = RestaurantPricingProfile::create([
            'id' => Str::uuid(),
            'restaurant_id' => $this->testRestaurant->id,
            'unified_pricing_setting_id' => $this->globalPricingSetting->id,
            'profile_name' => 'Test Profile',
            'profile_type' => 'standard',
            'is_active' => true,
            'activated_at' => now(),
        ]);

        // Order above free delivery threshold
        $orderData = [
            'restaurant_id' => $this->testRestaurant->id,
            'order_total' => 150.00, // Above 120.00 threshold
            'distance_km' => 5.0,
        ];

        $result = $this->pricingService->calculateOrderPricing($orderData);

        $this->assertEquals(0.00, $result['delivery_charge']);
    }

    /** @test */
    public function it_applies_min_max_delivery_charge_limits()
    {
        $profile = RestaurantPricingProfile::create([
            'id' => Str::uuid(),
            'restaurant_id' => $this->testRestaurant->id,
            'unified_pricing_setting_id' => $this->globalPricingSetting->id,
            'profile_name' => 'Test Profile',
            'profile_type' => 'standard',
            'is_active' => true,
            'activated_at' => now(),
        ]);

        // Test minimum delivery charge
        $orderData = [
            'restaurant_id' => $this->testRestaurant->id,
            'order_total' => 20.00,
            'distance_km' => 0.5, // Very short distance
        ];

        $result = $this->pricingService->calculateOrderPricing($orderData);

        // Should apply minimum delivery charge
        $this->assertGreaterThanOrEqual(6.00, $result['delivery_charge']);

        // Test maximum delivery charge
        $orderData['distance_km'] = 50.0; // Very long distance
        $result = $this->pricingService->calculateOrderPricing($orderData);

        // Should apply maximum delivery charge
        $this->assertLessThanOrEqual(60.00, $result['delivery_charge']);
    }

    /** @test */
    public function it_calculates_driver_commission_with_distance_rate()
    {
        $profile = RestaurantPricingProfile::create([
            'id' => Str::uuid(),
            'restaurant_id' => $this->testRestaurant->id,
            'unified_pricing_setting_id' => $this->globalPricingSetting->id,
            'profile_name' => 'Test Profile',
            'profile_type' => 'standard',
            'is_active' => true,
            'activated_at' => now(),
        ]);

        $orderData = [
            'restaurant_id' => $this->testRestaurant->id,
            'order_total' => 100.00,
            'distance_km' => 10.0,
        ];

        $result = $this->pricingService->calculateOrderPricing($orderData);

        // Expected: (order_total * driver_commission_rate / 100) + (distance * driver_distance_rate)
        // (100.00 * 8 / 100) + (10.0 * 0.75) = 8.00 + 7.50 = 15.50
        $expectedDriverCommission = (100.00 * 8 / 100) + (10.0 * 0.75);
        $this->assertEquals($expectedDriverCommission, $result['driver_commission']);
    }

    /** @test */
    public function it_applies_driver_commission_limits()
    {
        $profile = RestaurantPricingProfile::create([
            'id' => Str::uuid(),
            'restaurant_id' => $this->testRestaurant->id,
            'unified_pricing_setting_id' => $this->globalPricingSetting->id,
            'profile_name' => 'Test Profile',
            'profile_type' => 'standard',
            'is_active' => true,
            'activated_at' => now(),
        ]);

        // Test minimum driver commission
        $orderData = [
            'restaurant_id' => $this->testRestaurant->id,
            'order_total' => 10.00, // Very small order
            'distance_km' => 1.0,
        ];

        $result = $this->pricingService->calculateOrderPricing($orderData);

        // Should apply minimum driver commission
        $this->assertGreaterThanOrEqual(5.00, $result['driver_commission']);

        // Test maximum driver commission
        $orderData = [
            'restaurant_id' => $this->testRestaurant->id,
            'order_total' => 500.00, // Very large order
            'distance_km' => 20.0,
        ];

        $result = $this->pricingService->calculateOrderPricing($orderData);

        // Should apply maximum driver commission
        $this->assertLessThanOrEqual(30.00, $result['driver_commission']);
    }

    /** @test */
    public function it_throws_exception_when_no_restaurant_profile_found()
    {
        $orderData = [
            'restaurant_id' => Str::uuid(), // Non-existent restaurant
            'order_total' => 100.00,
            'distance_km' => 5.0,
        ];

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('No active pricing profile found for restaurant');

        $this->pricingService->calculateOrderPricing($orderData);
    }

    /** @test */
    public function it_can_get_restaurants_with_pricing_profiles()
    {
        // Create a profile for the test restaurant
        RestaurantPricingProfile::create([
            'id' => Str::uuid(),
            'restaurant_id' => $this->testRestaurant->id,
            'unified_pricing_setting_id' => $this->globalPricingSetting->id,
            'profile_name' => 'Test Profile',
            'profile_type' => 'custom',
            'custom_admin_commission_rate' => 15.00,
            'is_active' => true,
            'activated_at' => now(),
        ]);

        $restaurants = $this->pricingService->getRestaurantsWithPricingProfiles();

        $this->assertIsArray($restaurants);
        $this->assertNotEmpty($restaurants);
        
        $testRestaurantData = collect($restaurants)->firstWhere('restaurant_id', $this->testRestaurant->id);
        $this->assertNotNull($testRestaurantData);
        $this->assertTrue($testRestaurantData['has_profile']);
        $this->assertEquals('custom', $testRestaurantData['profile']['profile_type']);
        $this->assertTrue($testRestaurantData['profile']['has_custom_overrides']);
    }

    /** @test */
    public function it_can_update_restaurant_pricing_profile()
    {
        $profile = RestaurantPricingProfile::create([
            'id' => Str::uuid(),
            'restaurant_id' => $this->testRestaurant->id,
            'unified_pricing_setting_id' => $this->globalPricingSetting->id,
            'profile_name' => 'Original Profile',
            'profile_type' => 'standard',
            'is_active' => true,
            'activated_at' => now(),
        ]);

        $updateData = [
            'profile_name' => 'Updated Profile',
            'custom_admin_commission_rate' => 18.00,
            'custom_driver_commission_rate' => 12.00,
        ];

        $updatedProfile = $this->pricingService->updateRestaurantPricingProfile(
            $profile->id,
            $updateData
        );

        $this->assertEquals('Updated Profile', $updatedProfile->profile_name);
        $this->assertEquals(18.00, $updatedProfile->custom_admin_commission_rate);
        $this->assertEquals(12.00, $updatedProfile->custom_driver_commission_rate);
        $this->assertTrue($updatedProfile->hasCustomOverrides());
    }
}
