<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

/**
 * Migration for Direct Restaurant-Pricing Integration
 * Creates direct relationship between restaurants and unified pricing settings
 * Eliminates dependency on pricing templates
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Create restaurant_pricing_profiles table for direct integration
        Schema::create('restaurant_pricing_profiles', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('restaurant_id')->comment('معرف المطعم');
            $table->uuid('unified_pricing_setting_id')->comment('معرف إعدادات التسعير الموحد');
            
            // Profile metadata
            $table->string('profile_name')->comment('اسم الملف الشخصي');
            $table->text('description')->nullable()->comment('وصف الملف الشخصي');
            $table->enum('profile_type', ['standard', 'premium', 'custom'])->default('standard')->comment('نوع الملف الشخصي');
            
            // Commission overrides (optional - if restaurant needs custom rates)
            $table->decimal('custom_admin_commission_rate', 5, 2)->nullable()->comment('عمولة إدارة مخصصة');
            $table->decimal('custom_driver_commission_rate', 5, 2)->nullable()->comment('عمولة سائق مخصصة');
            $table->decimal('custom_delivery_base_charge', 8, 2)->nullable()->comment('رسوم توصيل أساسية مخصصة');
            
            // Status and timing
            $table->boolean('is_active')->default(true)->comment('حالة التفعيل');
            $table->timestamp('activated_at')->nullable()->comment('تاريخ التفعيل');
            $table->timestamp('expires_at')->nullable()->comment('تاريخ انتهاء الصلاحية');
            
            // Audit fields
            $table->uuid('created_by')->nullable()->comment('منشئ الملف');
            $table->uuid('updated_by')->nullable()->comment('محدث الملف');
            $table->timestamps();
            
            // Indexes
            $table->index('restaurant_id');
            $table->index('unified_pricing_setting_id');
            $table->index(['restaurant_id', 'is_active']);
            $table->index('profile_type');
            
            // Foreign keys
            $table->foreign('restaurant_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('unified_pricing_setting_id')->references('id')->on('unified_pricing_settings')->onDelete('cascade');
        });

        // 2. Update unified_pricing_settings to support restaurant-specific settings
        Schema::table('unified_pricing_settings', function (Blueprint $table) {
            // Add restaurant-specific fields
            $table->boolean('is_restaurant_specific')->default(false)->after('scope_id')->comment('خاص بمطعم محدد');
            $table->uuid('target_restaurant_id')->nullable()->after('is_restaurant_specific')->comment('المطعم المستهدف');
            
            // Add template conversion flag
            $table->boolean('converted_from_template')->default(false)->after('target_restaurant_id')->comment('محول من قالب');
            $table->uuid('original_template_id')->nullable()->after('converted_from_template')->comment('معرف القالب الأصلي');
            
            // Add indexes
            $table->index('is_restaurant_specific');
            $table->index('target_restaurant_id');
            $table->index(['target_restaurant_id', 'is_active']);
        });

        // 3. Create default pricing profiles for existing restaurants
        // Note: Using existing global settings if available
        $this->createDefaultPricingProfiles();
    }

    /**
     * Create default pricing profiles for existing restaurants
     */
    private function createDefaultPricingProfiles(): void
    {
        try {
            // Get existing global pricing setting
            $defaultPricingSetting = DB::table('unified_pricing_settings')
                ->where('scope', 'global')
                ->where('is_active', true)
                ->first();

            if (!$defaultPricingSetting) {
                // Create a simple default if none exists
                $settingId = Str::uuid();
                DB::table('unified_pricing_settings')->insert([
                    'id' => $settingId,
                    'name' => 'default_global_pricing',
                    'display_name' => 'إعدادات التسعير العامة الافتراضية',
                    'description' => 'إعدادات التسعير والعمولة الافتراضية لجميع المطاعم',
                    'scope' => 'global',
                    'distance_unit' => 'km',
                    'max_delivery_distance' => 25.00,
                    'driver_search_radius' => 10.00,
                    'delivery_charge_type' => 'dynamic',
                    'base_delivery_charge' => 8.00,
                    'per_km_delivery_rate' => 1.50,
                    'min_delivery_charge' => 6.00,
                    'max_delivery_charge' => 60.00,
                    'free_delivery_above' => 120.00,
                    'commission_calculation_method' => 'percentage',
                    'admin_commission_rate' => 12.00,
                    'driver_commission_rate' => 8.00,
                    'driver_distance_rate' => 0.75,
                    'min_driver_commission' => 5.00,
                    'max_driver_commission' => 30.00,
                    'urgent_delivery_multiplier' => 1.80,
                    'is_active' => true,
                    'priority' => 1,
                    'effective_from' => now(),
                    'auto_sync_enabled' => true,
                    'is_restaurant_specific' => false,
                    'converted_from_template' => false,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $defaultPricingSetting = (object) ['id' => $settingId];
            }

            // Get all active restaurants
            $restaurants = DB::table('users')
                ->where('role', 'vendor')
                ->where('active', true)
                ->select('id', 'firstName', 'lastName')
                ->get();

            // Create profiles for each restaurant
            foreach ($restaurants as $restaurant) {
                $profileId = Str::uuid();

                DB::table('restaurant_pricing_profiles')->insert([
                    'id' => $profileId,
                    'restaurant_id' => $restaurant->id,
                    'unified_pricing_setting_id' => $defaultPricingSetting->id,
                    'profile_name' => "ملف {$restaurant->firstName} {$restaurant->lastName}",
                    'description' => 'ملف تسعير افتراضي تم إنشاؤه تلقائياً',
                    'profile_type' => 'standard',
                    'is_active' => true,
                    'activated_at' => now(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

        } catch (\Exception $e) {
            \Log::error('Error creating default pricing profiles: ' . $e->getMessage());
            // Don't throw - allow migration to continue
        }
    }

    // Removed unused methods

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove foreign keys first
        Schema::table('unified_pricing_settings', function (Blueprint $table) {
            $table->dropIndex(['is_restaurant_specific']);
            $table->dropIndex(['target_restaurant_id']);
            $table->dropIndex(['target_restaurant_id', 'is_active']);
            
            $table->dropColumn([
                'is_restaurant_specific',
                'target_restaurant_id',
                'converted_from_template',
                'original_template_id'
            ]);
        });

        Schema::dropIfExists('restaurant_pricing_profiles');
    }
};
