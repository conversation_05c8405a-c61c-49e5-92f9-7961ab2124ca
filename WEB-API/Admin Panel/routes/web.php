<?php

use Illuminate\Support\Facades\Route;

// Include Unified Pricing System Routes
require_once __DIR__ . '/unified_pricing.php';

// Test route for unified pricing access
Route::get('/test-unified-pricing', function () {
    return response()->json([
        'success' => true,
        'message' => 'Unified Pricing routes are accessible',
        'routes' => [
            'index' => route('unified-pricing.index'),
            'create' => route('unified-pricing.index'),
            'analytics' => route('unified-pricing.analytics'),
            'templates' => route('unified-pricing.index'),
            'sync' => route('unified-pricing.index'),
        ],
        'authentication' => [
            'admin_logged_in' => Auth::guard('admin')->check(),
            'admin_user' => Auth::guard('admin')->user() ? Auth::guard('admin')->user()->name : null,
        ],
        'timestamp' => now()
    ]);
});

// Comprehensive test route for unified pricing system
Route::get('/verify-unified-pricing', function () {
    $results = [];

    // Test all routes
    $routes = [
        'Main Page' => route('unified-pricing.index'),
        'Create New' => route('unified-pricing.index'),
        'Analytics' => route('unified-pricing.analytics'),
        'Templates' => route('unified-pricing.index'),
        'Sync' => route('unified-pricing.index'),
    ];

    foreach ($routes as $name => $url) {
        try {
            $response = Http::get($url);
            $results[$name] = [
                'url' => $url,
                'status' => $response->status(),
                'accessible' => $response->successful(),
            ];
        } catch (\Exception $e) {
            $results[$name] = [
                'url' => $url,
                'status' => 'error',
                'accessible' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    return response()->json([
        'success' => true,
        'message' => 'Unified Pricing System Verification Complete',
        'authentication' => [
            'admin_logged_in' => Auth::guard('admin')->check(),
            'admin_user' => Auth::guard('admin')->user() ? Auth::guard('admin')->user()->name : null,
            'has_permissions' => Auth::guard('admin')->check(),
        ],
        'routes_test' => $results,
        'timestamp' => now()
    ]);
});

// مسارات بدون middleware للاختبار والتشخيص
Route::group(['middleware' => []], function () {

    // صفحة اختبار بسيطة
    Route::get('/test', function () {
        return response()->json([
            'status' => 'success',
            'message' => 'Laravel server is working!',
            'timestamp' => date('Y-m-d H:i:s'),
            'server_info' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB'
            ]
        ]);
    });



    // صفحة اختبار WebSocket
    Route::get('/websocket-test', function () {
        $html = '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>اختبار WebSocket - Foodie v8.2</title>
    <style>
        body { font-family: Arial; background: #f8f9fa; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .status { padding: 10px; border-radius: 5px; margin: 10px 0; }
        .status.connected { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.disconnected { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .input-group { margin: 10px 0; }
        .input-group input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px; margin-left: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 اختبار WebSocket</h1>

        <div id="status" class="status disconnected">
            ❌ غير متصل
        </div>

        <div class="input-group">
            <label>عنوان الخادم:</label>
            <input type="text" id="serverUrl" value="ws://127.0.0.1:6002" />
            <button class="btn" onclick="connect()">اتصال</button>
            <button class="btn" onclick="disconnect()">قطع الاتصال</button>
        </div>

        <div class="input-group">
            <label>رسالة اختبار:</label>
            <input type="text" id="message" value="Hello WebSocket!" />
            <button class="btn" onclick="sendMessage()">إرسال</button>
        </div>

        <div class="input-group">
            <button class="btn" onclick="testOrderUpdate()">اختبار تحديث طلب</button>
            <button class="btn" onclick="clearLog()">مسح السجل</button>
        </div>

        <h3>📋 سجل الأحداث:</h3>
        <div id="log" class="log"></div>

        <div style="margin-top: 20px;">
            <a href="/" style="color: #007bff;">العودة للصفحة الرئيسية</a>
        </div>
    </div>

    <script>
        let socket = null;

        function log(message) {
            const logDiv = document.getElementById("log");
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(connected) {
            const statusDiv = document.getElementById("status");
            if (connected) {
                statusDiv.className = "status connected";
                statusDiv.innerHTML = "✅ متصل";
            } else {
                statusDiv.className = "status disconnected";
                statusDiv.innerHTML = "❌ غير متصل";
            }
        }

        function connect() {
            const url = document.getElementById("serverUrl").value;
            log(`محاولة الاتصال بـ: ${url}`);

            try {
                socket = new WebSocket(url);

                socket.onopen = function(event) {
                    log("✅ تم الاتصال بنجاح!");
                    updateStatus(true);
                };

                socket.onmessage = function(event) {
                    log(`📨 رسالة واردة: ${event.data}`);
                };

                socket.onclose = function(event) {
                    log(`🔌 تم قطع الاتصال. الكود: ${event.code}`);
                    updateStatus(false);
                };

                socket.onerror = function(error) {
                    log(`❌ خطأ في الاتصال: ${error}`);
                    updateStatus(false);
                };

            } catch (error) {
                log(`❌ خطأ: ${error.message}`);
                updateStatus(false);
            }
        }

        function disconnect() {
            if (socket) {
                socket.close();
                socket = null;
                log("🔌 تم قطع الاتصال يدوياً");
                updateStatus(false);
            }
        }

        function sendMessage() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                const message = document.getElementById("message").value;
                socket.send(message);
                log(`📤 تم إرسال: ${message}`);
            } else {
                log("❌ لا يوجد اتصال نشط");
            }
        }

        function testOrderUpdate() {
            log("🧪 اختبار تحديث طلب...");
            const orderData = {
                type: "order_update",
                order_id: "order_" + Math.random().toString(36).substr(2, 9),
                status: "preparing",
                timestamp: new Date().toISOString()
            };

            if (socket && socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify(orderData));
                log(`📤 تم إرسال تحديث الطلب: ${JSON.stringify(orderData)}`);
            } else {
                log("❌ لا يوجد اتصال نشط لإرسال تحديث الطلب");
            }
        }

        function clearLog() {
            document.getElementById("log").innerHTML = "";
        }

        window.onload = function() {
            log("🚀 صفحة اختبار WebSocket جاهزة");
            log("💡 انقر على زر الاتصال لبدء الاختبار");
        };
    </script>
</body>
</html>';

        return response($html, 200, ['Content-Type' => 'text/html; charset=utf-8']);
    });

    // صفحة حالة الخوادم المحدثة
    Route::get('/servers-status', function () {
        $html = '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>حالة الخوادم - Foodie v8.2</title>
    <style>
        body { font-family: Arial; background: #f8f9fa; margin: 0; padding: 20px; }
        .container { max-width: 1000px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .server-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 15px 0; }
        .status { padding: 8px 15px; border-radius: 20px; display: inline-block; margin: 5px 0; font-weight: bold; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .status.checking { background: #fff3cd; color: #856404; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; border: none; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .refresh-btn { background: #28a745; }
        .test-btn { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖥️ حالة خوادم Foodie v8.2</h1>
        <p>مراقبة حالة جميع الخوادم والخدمات - محدث</p>

        <div style="text-align: center; margin: 20px 0;">
            <button class="btn refresh-btn" onclick="refreshAll()">🔄 تحديث جميع الحالات</button>
        </div>

        <div class="server-card">
            <h3>🌐 خادم Laravel الرئيسي</h3>
            <div id="laravel-status" class="status checking">🔄 جاري الفحص...</div>
            <p><strong>العنوان:</strong> http://127.0.0.1:8001</p>
            <p><strong>اختبار API:</strong> http://127.0.0.1:8001/test</p>
            <a href="http://127.0.0.1:8001/test" class="btn" target="_blank">اختبار API</a>
            <button class="btn test-btn" onclick="testServer(\'http://127.0.0.1:8001/test\', \'laravel-status\')">فحص</button>
        </div>

        <div class="server-card">
            <h3>🔌 خادم WebSocket</h3>
            <div id="websocket-status" class="status checking">🔄 جاري الفحص...</div>
            <p><strong>العنوان:</strong> http://127.0.0.1:6002</p>
            <p><strong>Health Check:</strong> http://127.0.0.1:6002/health</p>
            <a href="http://127.0.0.1:6002/health" class="btn" target="_blank">Health Check</a>
            <button class="btn test-btn" onclick="testServer(\'http://127.0.0.1:6002/health\', \'websocket-status\')">فحص</button>
        </div>

        <div class="server-card">
            <h3>🧪 صفحة اختبار WebSocket</h3>
            <div id="test-page-status" class="status checking">🔄 جاري الفحص...</div>
            <p><strong>العنوان:</strong> http://127.0.0.1:8001/websocket-test</p>
            <a href="http://127.0.0.1:8001/websocket-test" class="btn" target="_blank">فتح صفحة الاختبار</a>
            <button class="btn test-btn" onclick="testServer(\'http://127.0.0.1:8001/websocket-test\', \'test-page-status\')">فحص</button>
        </div>

        <div style="margin-top: 30px; padding: 15px; background: #e9ecef; border-radius: 5px;">
            <h4>📋 معلومات مهمة:</h4>
            <ul style="text-align: right;">
                <li><strong>خادم Laravel:</strong> المنفذ 8001</li>
                <li><strong>خادم WebSocket:</strong> المنفذ 6002</li>
                <li><strong>WebSocket URL:</strong> ws://127.0.0.1:6002</li>
                <li>استخدم 127.0.0.1 بدلاً من localhost</li>
            </ul>
        </div>

        <div style="margin-top: 20px; text-align: center;">
            <a href="/websocket-test" class="btn">اختبار WebSocket</a>
            <a href="/" class="btn">العودة للصفحة الرئيسية</a>
        </div>
    </div>

    <script>
        function testServer(url, statusElementId) {
            const statusElement = document.getElementById(statusElementId);
            statusElement.className = "status checking";
            statusElement.textContent = "🔄 جاري الفحص...";

            fetch(url, {
                method: "GET",
                cache: "no-cache"
            })
            .then(response => {
                if (response.ok) {
                    statusElement.className = "status online";
                    statusElement.textContent = "✅ متصل";
                } else {
                    statusElement.className = "status offline";
                    statusElement.textContent = "❌ خطأ HTTP " + response.status;
                }
            })
            .catch(error => {
                statusElement.className = "status offline";
                statusElement.textContent = "❌ غير متصل";
            });
        }

        function refreshAll() {
            testServer("http://127.0.0.1:8001/test", "laravel-status");
            testServer("http://127.0.0.1:6002/health", "websocket-status");
            testServer("http://127.0.0.1:8001/websocket-test", "test-page-status");
        }

        // فحص أولي عند تحميل الصفحة
        window.onload = function() {
            setTimeout(refreshAll, 1000);
        };

        // تحديث تلقائي كل 30 ثانية
        setInterval(refreshAll, 30000);
    </script>
</body>
</html>';

        return response($html, 200, ['Content-Type' => 'text/html; charset=utf-8']);
    });

    // مسارات WebSocket API
    Route::prefix('api/websocket')->group(function () {
        Route::post('/register-connection', [App\Http\Controllers\WebSocketController::class, 'registerConnection']);
        Route::post('/unregister-connection', [App\Http\Controllers\WebSocketController::class, 'unregisterConnection']);
        Route::post('/update-ping', [App\Http\Controllers\WebSocketController::class, 'updatePing']);
        Route::post('/send-to-connection', [App\Http\Controllers\WebSocketController::class, 'sendToConnection']);
        Route::post('/send-to-channel', [App\Http\Controllers\WebSocketController::class, 'sendToChannel']);
        Route::post('/send-notification', [App\Http\Controllers\WebSocketController::class, 'sendNotification']);
    });

    // مسار مصادقة Laravel Echo Server (بدون CSRF للتوافق مع Echo Server)
    Route::post('/broadcasting/auth', function(\Illuminate\Http\Request $request) {
        $channelName = $request->input('channel_name', '');
        $socketId = $request->input('socket_id', '');

        // للقنوات العامة، نسمح بالوصول مباشرة
        if (!str_starts_with($channelName, 'private-') && !str_starts_with($channelName, 'presence-')) {
            return response()->json([
                'auth' => hash_hmac('sha256', $socketId . ':' . $channelName, env('ECHO_SERVER_APP_SECRET', 'your-secret-key')),
                'channel_data' => json_encode([
                    'user_id' => 'guest',
                    'user_info' => [
                        'name' => 'Guest User'
                    ]
                ])
            ]);
        }

        // للقنوات الخاصة، نحتاج مصادقة المدير
        if (auth()->guard('admin')->check()) {
            $user = auth()->guard('admin')->user();

            // التحقق من صلاحية الوصول للقناة
            $canAccess = false;

            if (str_contains($channelName, 'admin-dashboard') ||
                str_contains($channelName, 'admin.') ||
                str_contains($channelName, 'order-updates') ||
                str_contains($channelName, 'private-admin')) {
                $canAccess = true; // جميع المدراء يمكنهم الوصول
            }

            if ($canAccess) {
                return response()->json([
                    'auth' => hash_hmac('sha256', $socketId . ':' . $channelName, 'local-secret'),
                    'channel_data' => json_encode([
                        'user_id' => $user->id,
                        'user_info' => [
                            'name' => $user->name,
                            'role' => $user->role ?? 'admin'
                        ]
                    ])
                ]);
            }
        }

        return response()->json(['error' => 'Unauthorized'], 403);
        Route::get('/active-connections', [App\Http\Controllers\WebSocketController::class, 'getActiveConnections']);
        Route::get('/unread-notifications', [App\Http\Controllers\WebSocketController::class, 'getUnreadNotifications']);
        Route::post('/mark-notification-read/{notificationId}', [App\Http\Controllers\WebSocketController::class, 'markNotificationAsRead']);
        Route::get('/stats', [App\Http\Controllers\WebSocketController::class, 'getStats']);
        Route::post('/cleanup-connections', [App\Http\Controllers\WebSocketController::class, 'cleanupConnections']);
        Route::post('/test-message', [App\Http\Controllers\WebSocketController::class, 'testMessage']);
    });

    // مسارات تتبع السائقين
    Route::prefix('api/driver-tracking')->group(function () {
        Route::post('/update-location', [App\Http\Controllers\DriverTrackingController::class, 'updateLocation']);
        Route::get('/driver/{driverId}/location', [App\Http\Controllers\DriverTrackingController::class, 'getDriverLocation']);
        Route::get('/active-drivers', [App\Http\Controllers\DriverTrackingController::class, 'getActiveDrivers']);
        Route::post('/nearby-drivers', [App\Http\Controllers\DriverTrackingController::class, 'getNearbyDrivers']);
        Route::get('/driver/{driverId}/route', [App\Http\Controllers\DriverTrackingController::class, 'getDriverRoute']);
        Route::patch('/driver/{driverId}/status', [App\Http\Controllers\DriverTrackingController::class, 'updateDriverStatus']);
    });

    // مسارات الدردشة المباشرة
    Route::prefix('api/realtime-chat')->group(function () {
        Route::post('/send-message', [App\Http\Controllers\RealtimeChatController::class, 'sendMessage']);
        Route::get('/chat/{chatId}/messages', [App\Http\Controllers\RealtimeChatController::class, 'getChatMessages']);
        Route::get('/messages', [App\Http\Controllers\RealtimeChatController::class, 'getRecentMessages']);
        Route::post('/mark-messages-read', [App\Http\Controllers\RealtimeChatController::class, 'markMessagesAsRead']);
        Route::get('/user/{userId}/{userType}/chats', [App\Http\Controllers\RealtimeChatController::class, 'getUserChats']);
        Route::post('/create-chat-id', [App\Http\Controllers\RealtimeChatController::class, 'createChatId']);
    });

    // مسارات لوحة التحكم المباشرة (محمية بـ authentication)
    Route::middleware(['auth:admin'])->prefix('api/realtime-dashboard')->group(function () {
        Route::get('/stats', [App\Http\Controllers\RealtimeDashboardController::class, 'getDashboardStats']);
        Route::get('/live-orders', [App\Http\Controllers\RealtimeDashboardController::class, 'getLiveOrders']);
        Route::get('/live-drivers', [App\Http\Controllers\RealtimeDashboardController::class, 'getLiveDrivers']);
        Route::get('/recent-notifications', [App\Http\Controllers\RealtimeDashboardController::class, 'getRecentNotifications']);
        Route::get('/active-connections', [App\Http\Controllers\RealtimeDashboardController::class, 'getActiveConnections']);

        // إضافة endpoints مفقودة للإشعارات
        Route::get('/notifications', [App\Http\Controllers\RealtimeDashboardController::class, 'getNotifications']);
        Route::get('/notification-stats', [App\Http\Controllers\RealtimeDashboardController::class, 'getNotificationStats']);
        Route::post('/send-notification', [App\Http\Controllers\RealtimeDashboardController::class, 'sendNotification']);
        Route::delete('/notifications/{id}', [App\Http\Controllers\RealtimeDashboardController::class, 'deleteNotification']);
    });

    // Real-time Dashboard Routes (Web Interface)
    Route::middleware(['auth:admin'])->prefix('realtime')->group(function () {
        Route::get('dashboard', [App\Http\Controllers\RealtimeDashboardController::class, 'index'])->name('realtime.dashboard');
        Route::get('connections', [App\Http\Controllers\RealtimeDashboardController::class, 'connections'])->name('realtime.connections');
        Route::get('notifications', [App\Http\Controllers\RealtimeDashboardController::class, 'notifications'])->name('realtime.notifications');
    });

    // Test Laravel Echo Server
    Route::get('/test-echo', function () {
        try {
            broadcast(new \App\Events\TestEvent(['message' => 'Laravel Echo Server is working!', 'timestamp' => now()]));
            return response()->json(['status' => 'Event broadcasted successfully', 'timestamp' => now()]);
        } catch (\Exception $e) {
            return response()->json(['status' => 'Error', 'message' => $e->getMessage()], 500);
        }
    });

    // Test Echo Server Status
    Route::get('/test-echo-status', function () {
        try {
            // Check if Laravel Echo Server is running
            $echoServerHost = env('MIX_ECHO_SERVER_HOST', 'localhost');
            $echoServerPort = env('MIX_ECHO_SERVER_PORT', '6001');

            $connection = @fsockopen($echoServerHost, $echoServerPort, $errno, $errstr, 5);

            if ($connection) {
                fclose($connection);
                return response()->json([
                    'status' => 'success',
                    'message' => 'Laravel Echo Server is running',
                    'host' => $echoServerHost,
                    'port' => $echoServerPort,
                    'timestamp' => now()
                ]);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Laravel Echo Server is not running',
                    'host' => $echoServerHost,
                    'port' => $echoServerPort,
                    'error' => $errstr,
                    'timestamp' => now()
                ], 503);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Error checking Echo Server status: ' . $e->getMessage()
            ], 500);
        }
    });

    // Test Chat Message
    Route::get('/test-chat', function () {
        try {
            // إنشاء رسالة اختبار
            $testMessage = new \App\Models\ChatMessage([
                'id' => 'test_' . time(),
                'chat_id' => 'test_chat_admin',
                'sender_id' => 1,
                'sender_type' => 'admin',
                'receiver_id' => 2,
                'receiver_type' => 'customer',
                'message' => 'رسالة اختبار من لوحة التحكم - ' . now()->format('H:i:s'),
                'message_type' => 'text',
                'is_read' => false,
                'created_at' => now()
            ]);

            // محاكاة بيانات المرسل والمستقبل
            $sender = (object) ['id' => 1, 'name' => 'الإدارة'];
            $receiver = (object) ['id' => 2, 'firstName' => 'عميل', 'lastName' => 'تجريبي'];

            // إرسال حدث الدردشة
            broadcast(new \App\Events\NewChatMessage($testMessage, $sender, $receiver));

            return response()->json([
                'status' => 'success',
                'message' => 'تم إرسال رسالة الدردشة التجريبية بنجاح',
                'chat_message' => [
                    'id' => $testMessage->id,
                    'message' => $testMessage->message,
                    'sender_type' => $testMessage->sender_type,
                    'created_at' => $testMessage->created_at
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'فشل في إرسال رسالة الدردشة: ' . $e->getMessage()
            ], 500);
        }
    });

    // Test Echo Server Page
    Route::get('/test-echo-page', function () {
        return view('test-echo');
    });

    // Real-time API Routes
    Route::middleware(['auth:admin'])->prefix('api/realtime-dashboard')->group(function () {
        // Dashboard data
        Route::get('dashboard-data', [App\Http\Controllers\RealtimeDashboardController::class, 'getDashboardData']);
        Route::get('dashboard-stats', [App\Http\Controllers\RealtimeDashboardController::class, 'getDashboardStats']);

        // Connections management
        Route::get('active-connections', [App\Http\Controllers\RealtimeDashboardController::class, 'getActiveConnections']);
        Route::get('connection/{id}', [App\Http\Controllers\RealtimeDashboardController::class, 'getConnectionDetails']);
        Route::post('send-message', [App\Http\Controllers\RealtimeDashboardController::class, 'sendMessage']);
        Route::post('disconnect/{id}', [App\Http\Controllers\RealtimeDashboardController::class, 'disconnectConnection']);
        Route::post('disconnect-all', [App\Http\Controllers\RealtimeDashboardController::class, 'disconnectAll']);

        // Notifications management
        Route::get('notifications', [App\Http\Controllers\RealtimeDashboardController::class, 'getNotifications']);
        Route::get('notification-stats', [App\Http\Controllers\RealtimeDashboardController::class, 'getNotificationStats']);
        Route::get('notification/{id}', [App\Http\Controllers\RealtimeDashboardController::class, 'getNotificationDetails']);
        Route::post('send-notification', [App\Http\Controllers\RealtimeDashboardController::class, 'sendNotification']);
        Route::post('resend-notification/{id}', [App\Http\Controllers\RealtimeDashboardController::class, 'resendNotification']);
        Route::delete('notification/{id}', [App\Http\Controllers\RealtimeDashboardController::class, 'deleteNotification']);
    });

    // Legacy Real-time Features Routes
    Route::middleware(['auth'])->prefix('api/realtime')->group(function () {
        Route::get('/dashboard-stats', [App\Http\Controllers\RealtimeController::class, 'getDashboardStats']);
        Route::post('/chat/send', [App\Http\Controllers\RealtimeController::class, 'sendChatMessage']);
        Route::get('/chat/messages', [App\Http\Controllers\RealtimeController::class, 'getChatMessages']);
        Route::put('/orders/{id}/status', [App\Http\Controllers\RealtimeController::class, 'updateOrderStatus']);
    });

    // Real-time Orders API Routes
    Route::middleware(['auth'])->prefix('api/orders')->group(function () {
        Route::put('/{id}/status', [App\Http\Controllers\Api\OrderController::class, 'updateStatus']);
    });

});
use Illuminate\Http\Request;
use App\Http\Controllers\SyncMonitoringController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Login route
Route::get('login', [App\Http\Controllers\Auth\LoginController::class, 'showLoginForm'])->name('login');
Route::post('login', [App\Http\Controllers\Auth\LoginController::class, 'login']);

// Register other auth routes except login
Route::get('register', [App\Http\Controllers\Auth\RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('register', [App\Http\Controllers\Auth\RegisterController::class, 'register']);
Route::post('logout', [App\Http\Controllers\Auth\LoginController::class, 'logout'])->name('logout');

// Password reset routes
Route::get('password/reset', [App\Http\Controllers\Auth\ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
Route::post('password/email', [App\Http\Controllers\Auth\ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
Route::get('password/reset/{token}', [App\Http\Controllers\Auth\ResetPasswordController::class, 'showResetForm'])->name('password.reset');
Route::post('password/reset', [App\Http\Controllers\Auth\ResetPasswordController::class, 'reset'])->name('password.update');

// Email verification routes
Route::get('email/verify', [App\Http\Controllers\Auth\VerificationController::class, 'show'])->name('verification.notice');
Route::get('email/verify/{id}/{hash}', [App\Http\Controllers\Auth\VerificationController::class, 'verify'])->name('verification.verify');
Route::post('email/resend', [App\Http\Controllers\Auth\VerificationController::class, 'resend'])->name('verification.resend');



// صفحة ترحيب بسيطة بدون تسجيل دخول - بدون قاعدة بيانات
Route::get('/', function () {
    return response('<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>Foodie v8.2 - نظام إدارة توصيل الطعام</title>
    <style>
        body { font-family: Arial; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; margin: 0; display: flex; align-items: center; justify-content: center; }
        .container { background: white; border-radius: 20px; padding: 40px; text-align: center; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 500px; }
        .logo { font-size: 4rem; margin-bottom: 20px; }
        .title { color: #333; font-size: 2.5rem; margin-bottom: 10px; font-weight: bold; }
        .subtitle { color: #666; font-size: 1.2rem; margin-bottom: 30px; }
        .status { background: #28a745; color: white; padding: 10px 20px; border-radius: 20px; display: inline-block; margin-bottom: 20px; }
        .btn { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 12px 30px; border: none; border-radius: 25px; text-decoration: none; display: inline-block; margin: 10px; cursor: pointer; }
        .info { background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🍕</div>
        <div class="status">✅ النظام يعمل بنجاح</div>
        <h1 class="title">Foodie v8.2</h1>
        <p class="subtitle">نظام إدارة توصيل الطعام المتكامل</p>
        <div class="info">
            <p><strong>الخادم:</strong> http://localhost:8001</p>
            <p><strong>الوقت:</strong> ' . date('Y-m-d H:i:s') . '</p>
            <p><strong>إصدار Laravel:</strong> ' . app()->version() . '</p>
            <p><strong>قاعدة البيانات:</strong> PostgreSQL متصلة</p>
        </div>
        <div>
            <a href="/hello" class="btn">اختبار بسيط</a>
            <a href="/test" class="btn">صفحة الاختبار</a>
            <a href="/db-test" class="btn">اختبار قاعدة البيانات</a>
            <a href="/diagnosis" class="btn">تشخيص النظام</a>
            <a href="/websocket-test" class="btn">اختبار WebSocket</a>
            <a href="/servers-status" class="btn">حالة الخوادم</a>
            <a href="/advanced-diagnosis" class="btn">تشخيص متقدم</a>
        </div>
    </div>
</body>
</html>', 200, ['Content-Type' => 'text/html; charset=utf-8']);
})->name('home');

// صفحة اختبار بسيطة
Route::get('/hello', function () {
    return response('<h1 style="text-align: center; color: #28a745; font-family: Arial;">مرحباً! Laravel يعمل بنجاح 🎉</h1>', 200, ['Content-Type' => 'text/html; charset=utf-8']);
});

// Removed duplicate test route - using the one above

// صفحة اختبار قاعدة البيانات
Route::get('/db-test', function () {
    try {
        $connection = DB::connection()->getPdo();
        $dbName = DB::connection()->getDatabaseName();
        $tables = DB::select("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'");

        $html = '<div style="text-align: center; font-family: Arial; padding: 50px;">
            <h1 style="color: #28a745;">✅ اختبار قاعدة البيانات نجح</h1>
            <p><strong>اسم قاعدة البيانات:</strong> ' . $dbName . '</p>
            <p><strong>عدد الجداول:</strong> ' . count($tables) . '</p>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px auto; max-width: 600px;">
                <h3>الجداول الموجودة:</h3>';

        foreach($tables as $table) {
            $html .= '<p>• ' . $table->table_name . '</p>';
        }

        $html .= '</div>
            <p><a href="/" style="color: #007bff;">العودة للصفحة الرئيسية</a></p>
        </div>';

        return response($html, 200, ['Content-Type' => 'text/html; charset=utf-8']);
    } catch (Exception $e) {
        return response('<div style="text-align: center; font-family: Arial; padding: 50px;">
            <h1 style="color: #dc3545;">❌ خطأ في قاعدة البيانات</h1>
            <p>' . $e->getMessage() . '</p>
            <p><a href="/" style="color: #007bff;">العودة للصفحة الرئيسية</a></p>
        </div>', 500, ['Content-Type' => 'text/html; charset=utf-8']);
    }
});

// صفحة تشخيص شاملة
Route::get('/diagnosis', function () {
    $info = [
        'laravel_version' => app()->version(),
        'php_version' => PHP_VERSION,
        'server_time' => date('Y-m-d H:i:s'),
        'timezone' => date_default_timezone_get(),
        'memory_usage' => memory_get_usage(true) / 1024 / 1024 . ' MB',
        'memory_limit' => ini_get('memory_limit'),
    ];

    $html = '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>تشخيص النظام - Foodie v8.2</title>
    <style>
        body { font-family: Arial; background: #f8f9fa; margin: 0; padding: 20px; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .info-card { background: #f8f9fa; padding: 15px; border-radius: 8px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص النظام</h1>
            <p class="status-ok">✅ Laravel يعمل بنجاح</p>
        </div>

        <div class="info-grid">
            <div class="info-card">
                <h3>معلومات Laravel</h3>
                <p><strong>الإصدار:</strong> ' . $info['laravel_version'] . '</p>
                <p><strong>البيئة:</strong> ' . app()->environment() . '</p>
            </div>

            <div class="info-card">
                <h3>معلومات الخادم</h3>
                <p><strong>PHP:</strong> ' . $info['php_version'] . '</p>
                <p><strong>الذاكرة:</strong> ' . $info['memory_usage'] . '</p>
            </div>

            <div class="info-card">
                <h3>الوقت والتاريخ</h3>
                <p><strong>الوقت:</strong> ' . $info['server_time'] . '</p>
                <p><strong>المنطقة الزمنية:</strong> ' . $info['timezone'] . '</p>
            </div>

            <div class="info-card">
                <h3>حالة قاعدة البيانات</h3>';

    try {
        $connection = DB::connection()->getPdo();
        $dbName = DB::connection()->getDatabaseName();
        $html .= '<p class="status-ok">✅ متصلة</p>
                <p><strong>قاعدة البيانات:</strong> ' . $dbName . '</p>';
    } catch (Exception $e) {
        $html .= '<p class="status-error">❌ غير متصلة</p>
                <p><strong>الخطأ:</strong> ' . $e->getMessage() . '</p>';
    }

    $html .= '    </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/" class="btn">الصفحة الرئيسية</a>
            <a href="/hello" class="btn">اختبار بسيط</a>
            <a href="/test" class="btn">صفحة الاختبار</a>
            <a href="/db-test" class="btn">اختبار قاعدة البيانات</a>
        </div>
    </div>
</body>
</html>';

    return response($html, 200, ['Content-Type' => 'text/html; charset=utf-8']);
});

// Removed duplicate websocket-test route - using the one above

// صفحة تشخيص الخوادم
Route::get('/servers-status', function () {
    $html = '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>حالة الخوادم - Foodie v8.2</title>
    <style>
        body { font-family: Arial; background: #f8f9fa; margin: 0; padding: 20px; }
        .container { max-width: 1000px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .server-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 15px 0; }
        .status { padding: 8px 15px; border-radius: 20px; display: inline-block; margin: 5px 0; font-weight: bold; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .status.checking { background: #fff3cd; color: #856404; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .info-item { background: white; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; text-align: center; }
        .refresh-btn { background: #28a745; }
        .test-btn { background: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖥️ حالة خوادم Foodie v8.2</h1>
        <p>مراقبة حالة جميع الخوادم والخدمات</p>

        <div class="info-grid">
            <div class="info-item">
                <h4>⏰ الوقت الحالي</h4>
                <p id="current-time">' . date('Y-m-d H:i:s') . '</p>
            </div>
            <div class="info-item">
                <h4>🔧 إصدار Laravel</h4>
                <p>' . app()->version() . '</p>
            </div>
            <div class="info-item">
                <h4>🐘 إصدار PHP</h4>
                <p>' . PHP_VERSION . '</p>
            </div>
            <div class="info-item">
                <h4>💾 استخدام الذاكرة</h4>
                <p>' . round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB</p>
            </div>
        </div>

        <div class="server-card">
            <h3>🌐 خادم Laravel الرئيسي</h3>
            <div id="laravel-status" class="status checking">🔄 جاري الفحص...</div>
            <p><strong>العنوان:</strong> http://127.0.0.1:8001</p>
            <p><strong>الوصف:</strong> خادم التطبيق الرئيسي</p>
            <a href="http://127.0.0.1:8001" class="btn" target="_blank">فتح الخادم</a>
            <button class="btn test-btn" onclick="testServer(\'http://127.0.0.1:8001\', \'laravel-status\')">اختبار</button>
        </div>

        <div class="server-card">
            <h3>🔌 خادم WebSocket</h3>
            <div id="websocket-status" class="status checking">🔄 جاري الفحص...</div>
            <p><strong>العنوان:</strong> http://127.0.0.1:6002</p>
            <p><strong>الوصف:</strong> خادم الاتصالات المباشرة</p>
            <a href="http://127.0.0.1:6002" class="btn" target="_blank">فتح الخادم</a>
            <button class="btn test-btn" onclick="testServer(\'http://127.0.0.1:6002/health\', \'websocket-status\')">اختبار</button>
        </div>

        <div class="server-card">
            <h3>🧪 صفحة اختبار WebSocket</h3>
            <div id="test-page-status" class="status checking">🔄 جاري الفحص...</div>
            <p><strong>العنوان:</strong> http://127.0.0.1:8001/websocket-test</p>
            <p><strong>الوصف:</strong> صفحة اختبار الاتصالات المباشرة</p>
            <a href="http://127.0.0.1:8001/websocket-test" class="btn" target="_blank">فتح صفحة الاختبار</a>
            <button class="btn test-btn" onclick="testServer(\'http://127.0.0.1:8001/websocket-test\', \'test-page-status\')">اختبار</button>
        </div>

        <div style="margin-top: 30px; text-align: center;">
            <button class="btn refresh-btn" onclick="refreshAll()">🔄 تحديث جميع الحالات</button>
            <a href="/" class="btn">العودة للصفحة الرئيسية</a>
        </div>

        <div style="margin-top: 20px; padding: 15px; background: #e9ecef; border-radius: 5px;">
            <h4>📋 ملاحظات مهمة:</h4>
            <ul style="text-align: right;">
                <li>استخدم <strong>127.0.0.1</strong> بدلاً من localhost للحصول على أفضل أداء</li>
                <li>تأكد من تشغيل كلا الخادمين قبل الاختبار</li>
                <li>خادم Laravel على المنفذ <strong>8001</strong></li>
                <li>خادم WebSocket على المنفذ <strong>6002</strong></li>
            </ul>
        </div>
    </div>

    <script>
        function updateTime() {
            document.getElementById("current-time").textContent = new Date().toLocaleString("ar-EG");
        }

        function testServer(url, statusElementId) {
            const statusElement = document.getElementById(statusElementId);
            statusElement.className = "status checking";
            statusElement.textContent = "🔄 جاري الفحص...";

            fetch(url, {
                method: "GET",
                mode: "no-cors",
                cache: "no-cache"
            })
            .then(response => {
                statusElement.className = "status online";
                statusElement.textContent = "✅ متصل";
            })
            .catch(error => {
                statusElement.className = "status offline";
                statusElement.textContent = "❌ غير متصل";
            });
        }

        function refreshAll() {
            testServer("http://127.0.0.1:8001", "laravel-status");
            testServer("http://127.0.0.1:6002/health", "websocket-status");
            testServer("http://127.0.0.1:8001/websocket-test", "test-page-status");
            updateTime();
        }

        // تحديث تلقائي كل 30 ثانية
        setInterval(updateTime, 1000);
        setInterval(refreshAll, 30000);

        // فحص أولي عند تحميل الصفحة
        window.onload = function() {
            setTimeout(refreshAll, 1000);
        };
    </script>
</body>
</html>';

    return response($html, 200, ['Content-Type' => 'text/html; charset=utf-8']);
});

// Removed duplicate test route - using the one above

// صفحة تشخيص متقدمة
Route::get('/advanced-diagnosis', function () {
    $html = '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>تشخيص متقدم - Foodie v8.2</title>
    <style>
        body { font-family: Arial; background: #f8f9fa; margin: 0; padding: 20px; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 10px; padding: 30px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .status-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; }
        .status { padding: 8px 15px; border-radius: 20px; display: inline-block; margin: 5px 0; font-weight: bold; }
        .status.online { background: #d4edda; color: #155724; }
        .status.offline { background: #f8d7da; color: #721c24; }
        .status.checking { background: #fff3cd; color: #856404; }
        .btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 5px; border: none; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .refresh-btn { background: #28a745; }
        .test-all-btn { background: #17a2b8; }
        .log-area { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; margin: 10px 0; }
        .info-item { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 تشخيص متقدم لنظام Foodie v8.2</h1>
        <p>فحص شامل لجميع مكونات النظام</p>

        <div style="text-align: center; margin: 20px 0;">
            <button class="btn refresh-btn" onclick="refreshAll()">🔄 تحديث جميع الحالات</button>
            <button class="btn test-all-btn" onclick="testAllServers()">🧪 اختبار جميع الخوادم</button>
            <button class="btn" onclick="clearLogs()">🗑️ مسح السجلات</button>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>🌐 خادم Laravel الرئيسي</h3>
                <div id="laravel-status" class="status checking">🔄 جاري الفحص...</div>
                <div class="info-item">
                    <strong>العنوان:</strong> http://127.0.0.1:8001<br>
                    <strong>الحالة:</strong> <span id="laravel-response-time">-</span><br>
                    <strong>آخر فحص:</strong> <span id="laravel-last-check">-</span>
                </div>
                <a href="http://127.0.0.1:8001" class="btn" target="_blank">فتح الخادم</a>
                <button class="btn" onclick="testLaravelServer()">اختبار</button>
            </div>

            <div class="status-card">
                <h3>🔌 خادم WebSocket</h3>
                <div id="websocket-status" class="status checking">🔄 جاري الفحص...</div>
                <div class="info-item">
                    <strong>العنوان:</strong> http://127.0.0.1:6002<br>
                    <strong>الحالة:</strong> <span id="websocket-response-time">-</span><br>
                    <strong>آخر فحص:</strong> <span id="websocket-last-check">-</span>
                </div>
                <a href="http://127.0.0.1:6002" class="btn" target="_blank">فتح الخادم</a>
                <button class="btn" onclick="testWebSocketServer()">اختبار</button>
            </div>

            <div class="status-card">
                <h3>🧪 صفحة اختبار WebSocket</h3>
                <div id="test-page-status" class="status checking">🔄 جاري الفحص...</div>
                <div class="info-item">
                    <strong>العنوان:</strong> http://127.0.0.1:8001/websocket-test<br>
                    <strong>الحالة:</strong> <span id="test-page-response-time">-</span><br>
                    <strong>آخر فحص:</strong> <span id="test-page-last-check">-</span>
                </div>
                <a href="http://127.0.0.1:8001/websocket-test" class="btn" target="_blank">فتح الصفحة</a>
                <button class="btn" onclick="testWebSocketPage()">اختبار</button>
            </div>

            <div class="status-card">
                <h3>📊 معلومات النظام</h3>
                <div class="info-item">
                    <strong>إصدار Laravel:</strong> ' . app()->version() . '<br>
                    <strong>إصدار PHP:</strong> ' . PHP_VERSION . '<br>
                    <strong>استخدام الذاكرة:</strong> ' . round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB<br>
                    <strong>الوقت:</strong> <span id="current-time">' . date('Y-m-d H:i:s') . '</span>
                </div>
            </div>
        </div>

        <div style="margin-top: 30px;">
            <h3>📋 سجل الأحداث</h3>
            <div id="log-area" class="log-area"></div>
        </div>

        <div style="margin-top: 20px; text-align: center;">
            <a href="/" class="btn">العودة للصفحة الرئيسية</a>
            <a href="/servers-status" class="btn">صفحة حالة الخوادم</a>
        </div>
    </div>

    <script>
        function log(message) {
            const logArea = document.getElementById("log-area");
            const time = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${time}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }

        function updateTime() {
            document.getElementById("current-time").textContent = new Date().toLocaleString("ar-EG");
        }

        function testServer(url, statusElementId, responseTimeElementId, lastCheckElementId) {
            const statusElement = document.getElementById(statusElementId);
            const responseTimeElement = document.getElementById(responseTimeElementId);
            const lastCheckElement = document.getElementById(lastCheckElementId);

            statusElement.className = "status checking";
            statusElement.textContent = "🔄 جاري الفحص...";

            const startTime = Date.now();

            fetch(url, {
                method: "GET",
                cache: "no-cache",
                headers: {
                    "Cache-Control": "no-cache",
                    "Pragma": "no-cache"
                }
            })
            .then(response => {
                const responseTime = Date.now() - startTime;
                statusElement.className = "status online";
                statusElement.textContent = "✅ متصل";
                responseTimeElement.textContent = `${responseTime}ms`;
                lastCheckElement.textContent = new Date().toLocaleTimeString();
                log(`✅ ${url} - متصل (${responseTime}ms)`);
                return response.text();
            })
            .then(data => {
                log(`📄 استجابة من ${url}: ${data.length} حرف`);
            })
            .catch(error => {
                const responseTime = Date.now() - startTime;
                statusElement.className = "status offline";
                statusElement.textContent = "❌ غير متصل";
                responseTimeElement.textContent = "خطأ";
                lastCheckElement.textContent = new Date().toLocaleTimeString();
                log(`❌ ${url} - خطأ: ${error.message}`);
            });
        }

        function testLaravelServer() {
            testServer("http://127.0.0.1:8001/test", "laravel-status", "laravel-response-time", "laravel-last-check");
        }

        function testWebSocketServer() {
            testServer("http://127.0.0.1:6002/health", "websocket-status", "websocket-response-time", "websocket-last-check");
        }

        function testWebSocketPage() {
            testServer("http://127.0.0.1:8001/websocket-test", "test-page-status", "test-page-response-time", "test-page-last-check");
        }

        function testAllServers() {
            log("🧪 بدء اختبار جميع الخوادم...");
            testLaravelServer();
            setTimeout(testWebSocketServer, 500);
            setTimeout(testWebSocketPage, 1000);
        }

        function refreshAll() {
            updateTime();
            testAllServers();
        }

        function clearLogs() {
            document.getElementById("log-area").innerHTML = "";
            log("🗑️ تم مسح السجلات");
        }

        // تحديث تلقائي
        setInterval(updateTime, 1000);
        setInterval(refreshAll, 30000);

        // فحص أولي
        window.onload = function() {
            log("🚀 صفحة التشخيص المتقدم جاهزة");
            setTimeout(refreshAll, 1000);
        };
    </script>
</body>
</html>';

    return response($html, 200, ['Content-Type' => 'text/html; charset=utf-8']);
});

Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

// API للإحصائيات
Route::get('/api/dashboard-stats', function () {
    try {
        $stats = [
            'success' => true,
            'users' => DB::table('users')->count(),
            'foods' => DB::table('foods')->count(),
            'categories' => DB::table('categories')->count(),
            'zones' => DB::table('zones')->count(),
            'orders' => DB::table('orders')->count(),
            'coupons' => DB::table('coupons')->count(),
        ];
        return response()->json($stats);
    } catch (Exception $e) {
        return response()->json(['success' => false, 'error' => $e->getMessage()]);
    }
});

/*
|--------------------------------------------------------------------------
| MIGRATED TO routes/api.php with proper authentication
| The following routes have been moved to routes/api.php:
| - /api/users/* (with auth:admin middleware)
| - /api/wallet/* (with auth:admin middleware)
| - /api/dynamic-notifications/* (with auth:admin middleware)
| - /api/email-templates/* (with auth:admin middleware)
|--------------------------------------------------------------------------
*/

/*
|--------------------------------------------------------------------------
| MIGRATED: Orders zones route moved to routes/api.php
| New route: /api/orders/zones (with auth:admin middleware)
|--------------------------------------------------------------------------
*/

/*
|--------------------------------------------------------------------------
| API Routes for Zones (No Auth Required for Admin Panel)
|--------------------------------------------------------------------------
*/
Route::prefix('api/zones')->group(function () {
    Route::get('/stats', [App\Http\Controllers\Api\ZoneController::class, 'stats']);
    Route::get('/published', [App\Http\Controllers\Api\ZoneController::class, 'getPublishedZones']);
    Route::post('/datatable', [App\Http\Controllers\Api\ZoneController::class, 'datatable']);
    Route::post('/bulk-delete', [App\Http\Controllers\Api\ZoneController::class, 'bulkDelete']);
    Route::post('/delete-multiple', [App\Http\Controllers\Api\ZoneController::class, 'destroyMultiple']);
    Route::get('/', [App\Http\Controllers\Api\ZoneController::class, 'index']);
    Route::post('/', [App\Http\Controllers\Api\ZoneController::class, 'store']);
    Route::get('/{id}', [App\Http\Controllers\Api\ZoneController::class, 'show'])->where('id', '[0-9]+');
    Route::put('/{id}', [App\Http\Controllers\Api\ZoneController::class, 'update'])->where('id', '[0-9]+');
    Route::put('/{id}/publish-status', [App\Http\Controllers\Api\ZoneController::class, 'updatePublishStatus'])->where('id', '[0-9]+');
    Route::delete('/{id}', [App\Http\Controllers\Api\ZoneController::class, 'destroy'])->where('id', '[0-9]+');
});



Route::get('lang/change', [App\Http\Controllers\LangController::class, 'change'])->name('changeLang');

// Debug route for menu permissions
Route::middleware('auth:admin')->get('/debug-menu', function() {
    return view('debug-menu');
})->name('debug.menu');

// Removed test language API route - not needed in production

// Debug route for language API
Route::get('/debug-languages', function() {
    return response()->file(public_path('debug_languages.html'));
});

// Language API endpoint removed - now handled by LanguageController in api.php

// مسارات مراقبة المزامنة
Route::prefix('admin/sync')->group(function () {
    Route::get('/dashboard', [SyncMonitoringController::class, 'dashboard'])->name('sync.dashboard');
    Route::get('/dashboard-data', [SyncMonitoringController::class, 'getDashboardData'])->name('sync.dashboard-data');
    Route::get('/collections-status', [SyncMonitoringController::class, 'getCollectionsStatus'])->name('sync.collections-status');
    Route::post('/switch-mode', [SyncMonitoringController::class, 'switchSyncMode'])->name('sync.switch-mode');
    Route::post('/trigger-sync', [SyncMonitoringController::class, 'triggerManualSync'])->name('sync.trigger');
    Route::post('/check-consistency', [SyncMonitoringController::class, 'checkConsistency'])->name('sync.check-consistency');
    Route::post('/clear-cache', [SyncMonitoringController::class, 'clearCache'])->name('sync.clear-cache');
    Route::get('/logs', [SyncMonitoringController::class, 'getSyncLogs'])->name('sync.logs');
    Route::get('/conflicts', [SyncMonitoringController::class, 'getConflicts'])->name('sync.conflicts');
    Route::post('/conflicts/{conflict}/resolve', [SyncMonitoringController::class, 'resolveConflict'])->name('sync.resolve-conflict');
});

// Removed sync test route - not needed in production

// مسار اختبار بسيط
Route::get('/sync-simple', function () {
    return view('sync-test');
})->name('sync.simple');

Route::post('payments/razorpay/createorder', [App\Http\Controllers\RazorPayController::class, 'createOrderid']);

Route::post('payments/getpaytmchecksum', [App\Http\Controllers\PaymentController::class, 'getPaytmChecksum']);

Route::post('payments/validatechecksum', [App\Http\Controllers\PaymentController::class, 'validateChecksum']);

Route::post('payments/initiatepaytmpayment', [App\Http\Controllers\PaymentController::class, 'initiatePaytmPayment']);

Route::get('payments/paytmpaymentcallback', [App\Http\Controllers\PaymentController::class, 'paytmPaymentcallback']);

Route::post('payments/paypalclientid', [App\Http\Controllers\PaymentController::class, 'getPaypalClienttoken']);

Route::post('payments/paypaltransaction', [App\Http\Controllers\PaymentController::class, 'createBraintreePayment']);

Route::post('payments/stripepaymentintent', [App\Http\Controllers\PaymentController::class, 'createStripePaymentIntent']);

Route::middleware('auth:admin')->group(function () {
    Route::get('termsAndConditions', [App\Http\Controllers\TermsAndConditionsController::class, 'index'])->name('termsAndConditions');
    Route::get('privacyPolicy', [App\Http\Controllers\TermsAndConditionsController::class, 'privacyindex'])->name('privacyPolicy');
});

Route::middleware('auth:admin')->group(function () {
    Route::get('/users', [App\Http\Controllers\HomeController::class, 'users'])->name('users');
    Route::get('/users', [App\Http\Controllers\UserController::class, 'index'])->name('users');

});
Route::middleware('auth:admin')->group(function () {
    Route::get('/users/edit/{id}', [App\Http\Controllers\UserController::class, 'edit'])->name('users.edit');
    Route::get('/users/create', [App\Http\Controllers\UserController::class, 'create'])->name('users.create');
    Route::get('/users/view/{id}', [App\Http\Controllers\UserController::class, 'view'])->name('users.view');
});

// User API Routes for Data Architecture Unification - Moved to routes/api.php
// These routes are now handled in api.php with proper authentication

// Dashboard API Routes (Firebase Replacement)
Route::prefix('api/dashboard')->group(function () {
    Route::get('currency/active', [App\Http\Controllers\Api\DashboardController::class, 'getActiveCurrency']);
    Route::get('orders/counts', [App\Http\Controllers\Api\DashboardController::class, 'getOrderCounts']);
    Route::get('products/count', [App\Http\Controllers\Api\DashboardController::class, 'getProductCount']);
    Route::get('earnings/total', [App\Http\Controllers\Api\DashboardController::class, 'getTotalEarnings']);
    Route::get('table-count/{table}', [App\Http\Controllers\Api\DashboardController::class, 'getTableCount']);
    Route::get('restaurants/top', [App\Http\Controllers\Api\DashboardController::class, 'getTopRestaurants']);
    Route::get('orders/recent', [App\Http\Controllers\Api\DashboardController::class, 'getRecentOrders']);
    Route::get('drivers/top', [App\Http\Controllers\Api\DashboardController::class, 'getTopDrivers']);
});

// Settings API Routes (PostgreSQL Direct)
Route::prefix('api/settings')->group(function () {
    Route::get('google-map-key', [App\Http\Controllers\Api\SettingsController::class, 'getGoogleMapKey']);
    Route::post('google-map-key', [App\Http\Controllers\Api\SettingsController::class, 'setGoogleMapKey']);
    Route::get('document-verification', [App\Http\Controllers\Api\SettingsController::class, 'getDocumentVerification']);
    Route::post('document-verification', [App\Http\Controllers\Api\SettingsController::class, 'setDocumentVerification']);
    Route::get('homepage-template', [App\Http\Controllers\Api\SettingsController::class, 'getHomepageTemplate']);
    Route::post('homepage-template', [App\Http\Controllers\Api\SettingsController::class, 'setHomepageTemplate']);
});



// Dashboard Routes (Direct Database Access)
Route::middleware('auth:admin')->group(function () {
    // Main dashboard route
    Route::get('dashboard', [App\Http\Controllers\HomeController::class, 'index'])->name('dashboard');

    Route::get('dashboard/orders/recent', [App\Http\Controllers\DashboardController::class, 'getRecentOrders'])->name('dashboard.orders.recent');
    Route::get('dashboard/drivers/top', [App\Http\Controllers\DashboardController::class, 'getTopDrivers'])->name('dashboard.drivers.top');
    Route::get('dashboard/restaurants/top', [App\Http\Controllers\DashboardController::class, 'getTopRestaurants'])->name('dashboard.restaurants.top');
    Route::get('dashboard/payouts/recent', [App\Http\Controllers\DashboardController::class, 'getRecentPayouts'])->name('dashboard.payouts.recent');
    Route::get('dashboard/statistics', [App\Http\Controllers\DashboardController::class, 'getStatistics'])->name('dashboard.statistics');
});

// Removed test routes - not needed in production

// Zone Routes (Direct Database Access)
Route::middleware('auth:admin')->group(function () {
    Route::get('zone/data', [App\Http\Controllers\ZoneController::class, 'getData'])->name('zone.data');
    Route::post('zone/store', [App\Http\Controllers\ZoneController::class, 'store'])->name('zone.store');
    Route::put('zone/{id}/publish-status', [App\Http\Controllers\ZoneController::class, 'updatePublishStatus'])->name('zone.publish-status');
    Route::delete('zone/{id}', [App\Http\Controllers\ZoneController::class, 'destroy'])->name('zone.destroy');
    Route::post('zone/delete-multiple', [App\Http\Controllers\ZoneController::class, 'destroyMultiple'])->name('zone.destroy-multiple');
    Route::get('zone/active', [App\Http\Controllers\ZoneController::class, 'getActiveZones'])->name('zone.active');
});

Route::middleware('auth:admin')->group(function () {
    Route::get('/vendors', [App\Http\Controllers\VendorController::class, 'index'])->name('vendors');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('/vendors/create', [App\Http\Controllers\VendorController::class, 'create'])->name('vendors.create');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('/vendors/approved', [App\Http\Controllers\VendorController::class, 'index'])->name('vendors.approved');
    Route::get('/vendors/pending', [App\Http\Controllers\VendorController::class, 'index'])->name('vendors.pending');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('/restaurants', [App\Http\Controllers\RestaurantController::class, 'index'])->name('restaurants');

});
Route::middleware('auth:admin')->group(function () {
    Route::get('/restaurants/create', [App\Http\Controllers\RestaurantController::class, 'create'])->name('restaurants.create');
    Route::get('/restaurants/edit/{id}', [App\Http\Controllers\RestaurantController::class, 'edit'])->name('restaurants.edit');
    Route::get('/restaurants/edit-new/{id}', [App\Http\Controllers\RestaurantController::class, 'editNew'])->name('restaurants.edit-new');
    Route::get('/restaurants/view/{id}', [App\Http\Controllers\RestaurantController::class, 'view'])->name('restaurants.view');
});
Route::get('/restaurants/promos/{id}', [App\Http\Controllers\RestaurantController::class, 'promos'])->name('restaurants.promos');

Route::middleware('auth:admin')->group(function () {
    Route::get('/coupons', [App\Http\Controllers\CouponController::class, 'index'])->name('coupons');
    Route::get('/coupon/{id}', [App\Http\Controllers\CouponController::class, 'index'])->name('restaurants.coupons');

});
Route::middleware('auth:admin')->group(function () {
    Route::get('/coupons/edit/{id}', [App\Http\Controllers\CouponController::class, 'edit'])->name('coupons.edit');
    Route::get('/coupons/create', [App\Http\Controllers\CouponController::class, 'create'])->name('coupons.create');
    Route::get('/coupon/create/{id}', [App\Http\Controllers\CouponController::class, 'create']);
    Route::get('/coupons/create/{id}', [App\Http\Controllers\CouponController::class, 'create']);
});

Route::middleware('auth:admin')->group(function () {
    Route::get('/foods', [App\Http\Controllers\FoodController::class, 'index'])->name('foods');
    Route::get('/foods/{id}', [App\Http\Controllers\FoodController::class, 'index'])->name('restaurants.foods');

});
Route::middleware('auth:admin')->group(function () {
    Route::get('/foods/edit/{id}', [App\Http\Controllers\FoodController::class, 'edit'])->name('foods.edit');
    Route::get('/food/create', [App\Http\Controllers\FoodController::class, 'create'])->name('foods.create');
    Route::get('/food/create/{id}', [App\Http\Controllers\FoodController::class, 'create']);
});

Route::middleware('auth:admin')->group(function () {
    Route::get('/orders/', [App\Http\Controllers\OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/', [App\Http\Controllers\OrderController::class, 'index'])->name('orders'); // إضافة alias للتوافق
    Route::get('/orders/{id}', [App\Http\Controllers\OrderController::class, 'index'])->name('restaurants.orders');

});
Route::middleware('auth:admin')->group(function () {
    Route::get('/orders/edit/{id}', [App\Http\Controllers\OrderController::class, 'edit'])->name('orders.edit');
    Route::get('/orders/print/{id}', [App\Http\Controllers\OrderController::class, 'orderprint'])->name('vendors.orderprint');
});

Route::middleware('auth:admin')->group(function () {

    Route::get('/categories', [App\Http\Controllers\CategoryController::class, 'index'])->name('categories');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('/categories/edit/{id}', [App\Http\Controllers\CategoryController::class, 'edit'])->name('categories.edit');
    Route::get('/categories/create', [App\Http\Controllers\CategoryController::class, 'create'])->name('categories.create');
});

// Temporary route without middleware for testing
Route::get('/drivers', [App\Http\Controllers\DriverController::class, 'index'])->name('drivers');

// Original route with middleware (commented out for testing)
// Route::middleware(['permission:drivers,drivers'])->group(function () {
//     Route::get('/drivers', [App\Http\Controllers\DriverController::class, 'index'])->name('drivers');
// });
Route::middleware('auth:admin')->group(function () {
    Route::get('/drivers/approved', [App\Http\Controllers\DriverController::class, 'index'])->name('drivers.approved');
    Route::get('/drivers/pending', [App\Http\Controllers\DriverController::class, 'index'])->name('drivers.pending');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('/drivers/edit/{id}', [App\Http\Controllers\DriverController::class, 'edit'])->name('drivers.edit');
    Route::get('/drivers/create', [App\Http\Controllers\DriverController::class, 'create'])->name('drivers.create');
    Route::post('/drivers', [App\Http\Controllers\DriverController::class, 'store'])->name('drivers.store');
    Route::get('/drivers/view/{id}', [App\Http\Controllers\DriverController::class, 'view'])->name('drivers.view');
    Route::get('/drivers/{id}', [App\Http\Controllers\DriverController::class, 'show'])->name('drivers.show');
    Route::put('/drivers/{id}', [App\Http\Controllers\DriverController::class, 'update'])->name('drivers.update');
    Route::delete('/drivers/{id}', [App\Http\Controllers\DriverController::class, 'destroy'])->name('drivers.destroy');
});


Route::get('/users/profile', [App\Http\Controllers\UserController::class, 'profile'])->name('users.profile');
Route::post('/users/profile/update/{id}', [App\Http\Controllers\UserController::class, 'update'])->name('users.profile.update');

Route::get('usersorders/{type}', [App\Http\Controllers\OrderController::class, 'index'])->name('usersorders');

Route::middleware('auth:admin')->group(function () {
    Route::get('/payments', [App\Http\Controllers\AdminPaymentsController::class, 'index'])->name('payments');
    Route::get('driverpayments', [App\Http\Controllers\AdminPaymentsController::class, 'driverIndex'])->name('driver.driverpayments');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('restaurantsPayouts', [App\Http\Controllers\RestaurantsPayoutController::class, 'index'])->name('restaurantsPayouts');
    Route::get('/restaurantsPayout/{id}', [App\Http\Controllers\RestaurantsPayoutController::class, 'index'])->name('restaurants.payout');
    Route::get('restaurantsPayouts/create', [App\Http\Controllers\RestaurantsPayoutController::class, 'create'])->name('restaurantsPayouts.create');
    Route::get('/restaurantsPayouts/create/{id}', [App\Http\Controllers\RestaurantsPayoutController::class, 'create']);

    Route::get('driversPayouts', [App\Http\Controllers\DriversPayoutController::class, 'index'])->name('driversPayouts');
    Route::get('driverPayout/{id}', [App\Http\Controllers\DriversPayoutController::class, 'index'])->name('driver.payout');
    Route::get('driversPayouts/create', [App\Http\Controllers\DriversPayoutController::class, 'create'])->name('driversPayouts.create');
    Route::get('driverPayout/create/{id}', [App\Http\Controllers\DriversPayoutController::class, 'create'])->name('driver.payout.create');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('walletstransaction', [App\Http\Controllers\TransactionController::class, 'index'])->name('walletstransaction');
    Route::get('/walletstransaction/{id}', [App\Http\Controllers\TransactionController::class, 'index'])->name('users.walletstransaction');
});
Route::post('order-status-notification', [App\Http\Controllers\OrderController::class, 'sendNotification'])->name('order-status-notification');

Route::middleware('auth:admin')->group(function () {
    Route::get('dynamic-notification', [App\Http\Controllers\DynamicNotificationController::class, 'index'])->name('dynamic-notification.index');
    Route::get('dynamic-notification/save/{id?}', [App\Http\Controllers\DynamicNotificationController::class, 'save'])->name('dynamic-notification.save');
    Route::get('dynamic-notification/delete/{id}', [App\Http\Controllers\DynamicNotificationController::class, 'delete'])->name('dynamic-notification.delete');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('/map', [App\Http\Controllers\MapController::class, 'index'])->name('map');
    Route::post('/map/get_order_info', [App\Http\Controllers\MapController::class, 'getOrderInfo'])->name('map.getOrderInfo');
});
Route::prefix('settings')->group(function () {
    Route::middleware('auth:admin')->group(function () {
        Route::get('/currencies', [App\Http\Controllers\CurrencyController::class, 'index'])->name('currencies');
        Route::get('/currencies/edit/{id}', [App\Http\Controllers\CurrencyController::class, 'edit'])->name('currencies.edit');
        Route::get('/currencies/create', [App\Http\Controllers\CurrencyController::class, 'create'])->name('currencies.create');
    });

    // Removed v2 routes - using standard pattern instead

    // Standard Settings Controller Routes (PostgreSQL-based)
    Route::middleware('auth:admin')->group(function () {
        Route::get('app/globals', [App\Http\Controllers\SettingsController::class, 'globals'])->name('settings.app.globals');
        Route::post('app/globals', [App\Http\Controllers\Api\SettingsController::class, 'updateGlobalSettings'])->name('settings.app.globals.update');
        // adminCommission route moved to UnifiedCommissionController
        Route::get('app/radiusConfiguration', [App\Http\Controllers\SettingsController::class, 'radiosConfiguration'])->name('settings.app.radiusConfiguration');
    });
    Route::middleware('auth:admin')->group(function () {
        Route::get('app/bookTable', [App\Http\Controllers\SettingsController::class, 'bookTable'])->name('settings.app.bookTable');
        Route::get('app/deliveryCharge', [App\Http\Controllers\SettingsController::class, 'deliveryCharge'])->name('settings.app.deliveryCharge');
        Route::get('app/documentVerification', [App\Http\Controllers\SettingsController::class, 'documentVerification'])->name('settings.app.documentVerification');
    });

    Route::middleware('auth:admin')->group(function () {
        Route::get('payment/stripe', [App\Http\Controllers\SettingsController::class, 'stripe'])->name('payment.stripe');
        Route::get('payment/applepay', [App\Http\Controllers\SettingsController::class, 'applepay'])->name('payment.applepay');
        Route::get('payment/razorpay', [App\Http\Controllers\SettingsController::class, 'razorpay'])->name('payment.razorpay');
        Route::get('payment/cod', [App\Http\Controllers\SettingsController::class, 'cod'])->name('payment.cod');
        Route::get('payment/paypal', [App\Http\Controllers\SettingsController::class, 'paypal'])->name('payment.paypal');
        Route::get('payment/paytm', [App\Http\Controllers\SettingsController::class, 'paytm'])->name('payment.paytm');
        Route::get('payment/wallet', [App\Http\Controllers\SettingsController::class, 'wallet'])->name('payment.wallet');
        Route::get('payment/payfast', [App\Http\Controllers\SettingsController::class, 'payfast'])->name('payment.payfast');
        Route::get('payment/paystack', [App\Http\Controllers\SettingsController::class, 'paystack'])->name('payment.paystack');
        Route::get('payment/flutterwave', [App\Http\Controllers\SettingsController::class, 'flutterwave'])->name('payment.flutterwave');
        Route::get('payment/mercadopago', [App\Http\Controllers\SettingsController::class, 'mercadopago'])->name('payment.mercadopago');
        Route::get('payment/xendit', [App\Http\Controllers\SettingsController::class, 'xendit'])->name('payment.xendit');
        Route::get('payment/orangepay', [App\Http\Controllers\SettingsController::class, 'orangepay'])->name('payment.orangepay');
        Route::get('payment/midtrans', [App\Http\Controllers\SettingsController::class, 'midtrans'])->name('payment.midtrans');
    });

    Route::middleware('auth:admin')->group(function () {
        Route::get('app/languages', [App\Http\Controllers\SettingsController::class, 'languages'])->name('settings.app.languages');
        Route::get('app/languages/create', [App\Http\Controllers\SettingsController::class, 'languagescreate'])->name('settings.app.languages.create');
        Route::get('app/languages/edit/{id}', [App\Http\Controllers\SettingsController::class, 'languagesedit'])->name('settings.app.languages.edit');
    });
    Route::middleware('auth:admin')->group(function () {
        Route::get('app/specialOffer', [App\Http\Controllers\SettingsController::class, 'specialOffer'])->name('setting.specialOffer');
    });

    Route::get('app/story', [App\Http\Controllers\SettingsController::class, 'story'])->name('setting.story');
    Route::get('app/notifications', [App\Http\Controllers\SettingsController::class, 'notifications'])->name('settings.app.notifications');
    Route::get('mobile/globals', [App\Http\Controllers\SettingsController::class, 'mobileGlobals'])->name('settings.mobile.globals');

});
Route::middleware('auth:admin')->group(function () {
    Route::get('/booktable/{id}', [App\Http\Controllers\BookTableController::class, 'index'])->name('restaurants.booktable');
    Route::get('/booktable/edit/{id}', [App\Http\Controllers\BookTableController::class, 'edit'])->name('booktable.edit');
});
Route::post('/sendnotification', [App\Http\Controllers\BookTableController::class, 'sendnotification'])->name('sendnotification');

Route::middleware('auth:admin')->group(function () {
    Route::get('/notification', [App\Http\Controllers\NotificationController::class, 'index'])->name('notification');
    Route::get('/notification/send', [App\Http\Controllers\NotificationController::class, 'send'])->name('notification.send');
});
Route::post('broadcastnotification', [App\Http\Controllers\NotificationController::class, 'broadcastnotification'])->name('broadcastnotification');

Route::middleware('auth:admin')->group(function () {
    Route::get('/payoutRequests/drivers', [App\Http\Controllers\PayoutRequestController::class, 'index'])->name('payoutRequests.drivers');
    Route::get('/payoutRequests/drivers/{id}', [App\Http\Controllers\PayoutRequestController::class, 'index'])->name('payoutRequests.drivers.view');
    Route::get('/payoutRequests/restaurants', [App\Http\Controllers\PayoutRequestController::class, 'restaurant'])->name('payoutRequests.restaurants');
    Route::get('/payoutRequests/restaurants/{id}', [App\Http\Controllers\PayoutRequestController::class, 'restaurant'])->name('payoutRequests.restaurants.view');
});
Route::get('order_transactions', [App\Http\Controllers\PaymentController::class, 'index'])->name('order_transactions');
Route::get('/order_transactions/{id}', [App\Http\Controllers\PaymentController::class, 'index'])->name('order_transactions.index');



Route::get('payment/success', [App\Http\Controllers\PaymentController::class, 'paymentsuccess'])->name('payment.success');
Route::get('payment/failed', [App\Http\Controllers\PaymentController::class, 'paymentfailed'])->name('payment.failed');
Route::get('payment/pending', [App\Http\Controllers\PaymentController::class, 'paymentpending'])->name('payment.pending');

Route::middleware('auth:admin')->group(function () {
    Route::get('/banners', [App\Http\Controllers\SettingsController::class, 'menuItems'])->name('setting.banners');
    Route::get('/banners/create', [App\Http\Controllers\SettingsController::class, 'menuItemsCreate'])->name('setting.banners.create');
    Route::get('/banners/edit/{id}', [App\Http\Controllers\SettingsController::class, 'menuItemsEdit'])->name('setting.banners.edit');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('/attributes', [App\Http\Controllers\AttributeController::class, 'index'])->name('attributes');
    Route::get('/attributes/edit/{id}', [App\Http\Controllers\AttributeController::class, 'edit'])->name('attributes.edit');
    Route::get('/attributes/create', [App\Http\Controllers\AttributeController::class, 'create'])->name('attributes.create');

    Route::get('/reviewattributes', [App\Http\Controllers\ReviewAttributeController::class, 'index'])->name('reviewattributes');
    Route::get('/reviewattributes/edit/{id}', [App\Http\Controllers\ReviewAttributeController::class, 'edit'])->name('reviewattributes.edit');
    Route::get('/reviewattributes/create', [App\Http\Controllers\ReviewAttributeController::class, 'create'])->name('reviewattributes.create');
});

Route::middleware('auth:admin')->group(function () {
    Route::get('footerTemplate', [App\Http\Controllers\SettingsController::class, 'footerTemplate'])->name('footerTemplate');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('/homepageTemplate', [App\Http\Controllers\SettingsController::class, 'homepageTemplate'])->name('homepageTemplate');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('cms', [App\Http\Controllers\CmsController::class, 'index'])->name('cms');
    Route::get('/cms/edit/{id}', [App\Http\Controllers\CmsController::class, 'edit'])->name('cms.edit');
    Route::get('/cms/create', [App\Http\Controllers\CmsController::class, 'create'])->name('cms.create');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('report/{type}', [App\Http\Controllers\ReportController::class, 'index'])->name('report.index');
});

Route::middleware('auth:admin')->group(function () {
    Route::get('/tax', [App\Http\Controllers\TaxController::class, 'index'])->name('tax');
    Route::get('/tax/edit/{id}', [App\Http\Controllers\TaxController::class, 'edit'])->name('tax.edit');
    Route::get('/tax/create', [App\Http\Controllers\TaxController::class, 'create'])->name('tax.create');
});

Route::middleware('auth:admin')->group(function () {
    Route::get('email-templates', [App\Http\Controllers\SettingsController::class, 'emailTemplatesIndex'])->name('email-templates.index');
    Route::get('email-templates/save/{id?}', [App\Http\Controllers\SettingsController::class, 'emailTemplatesSave'])->name('email-templates.save');
    Route::get('email-templates/delete/{id}', [App\Http\Controllers\SettingsController::class, 'emailTemplatesDelete'])->name('email-templates.delete');
});
Route::post('send-email', [App\Http\Controllers\SendEmailController::class, 'sendMail'])->name('sendMail');

Route::middleware('auth:admin')->group(function () {
    Route::get('gift-card', [App\Http\Controllers\GiftCardController::class, 'index'])->name('gift-card.index');
    Route::get('gift-card/save/{id?}', [App\Http\Controllers\GiftCardController::class, 'save'])->name('gift-card.save');
    Route::get('gift-card/edit/{id}', [App\Http\Controllers\GiftCardController::class, 'save'])->name('gift-card.edit');
});

Route::middleware('auth:admin')->group(function () {
    Route::get('role', [App\Http\Controllers\RoleController::class, 'index'])->name('role.index');
    Route::get('role/save', [App\Http\Controllers\RoleController::class, 'save'])->name('role.save');
    Route::post('role/store', [App\Http\Controllers\RoleController::class, 'store'])->name('role.store');
    Route::get('role/delete/{id}', [App\Http\Controllers\RoleController::class, 'delete'])->name('role.delete');
    Route::get('role/edit/{id}', [App\Http\Controllers\RoleController::class, 'edit'])->name('role.edit');
    Route::post('role/update/{id}', [App\Http\Controllers\RoleController::class, 'update'])->name('role.update');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('admin-users', [App\Http\Controllers\AdminUserController::class, 'index'])->name('admin.users');
    Route::get('admin-users/create', [App\Http\Controllers\AdminUserController::class, 'create'])->name('admin.users.create');
    Route::post('admin-users/store', [App\Http\Controllers\AdminUserController::class, 'store'])->name('admin.users.store');
    Route::get('admin-users/edit/{id}', [App\Http\Controllers\AdminUserController::class, 'edit'])->name('admin.users.edit');
    Route::post('admin-users/update/{id}', [App\Http\Controllers\AdminUserController::class, 'update'])->name('admin.users.update');
    Route::get('admin-users/delete/{id}', [App\Http\Controllers\AdminUserController::class, 'destroy'])->name('admin.users.delete');
    Route::get('admin-users/toggle-status/{id}', [App\Http\Controllers\AdminUserController::class, 'toggleStatus'])->name('admin.users.toggle.status');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('zone', [App\Http\Controllers\ZoneController::class, 'index'])->name('zone');
    Route::get('/zone/create', [App\Http\Controllers\ZoneController::class, 'create'])->name('zone.create');
    Route::get('/zone/edit/{id}', [App\Http\Controllers\ZoneController::class, 'edit'])->name('zone.edit');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('/documents/edit/{id}', [App\Http\Controllers\DocumentController::class, 'edit'])->name('documents.edit');
    Route::get('/documents/create', [App\Http\Controllers\DocumentController::class, 'create'])->name('documents.create');
    Route::get('documents', [App\Http\Controllers\DocumentController::class, 'index'])->name('documents');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('vendors/document-list/{id}', [App\Http\Controllers\VendorController::class, 'documentList'])->name('vendors.document');
    Route::get('/vendors/document/upload/{vendorId}/{id}', [App\Http\Controllers\VendorController::class, 'documentUpload'])->name('vendors.document.upload');
    Route::get('drivers/document-list/{id}', [App\Http\Controllers\DriverController::class, 'DocumentList'])->name('drivers.document');
    Route::get('/drivers/document/upload/{driverId}/{id}', [App\Http\Controllers\DriverController::class, 'DocumentUpload'])->name('drivers.document.upload');
});
Route::post('send-notification', [App\Http\Controllers\NotificationController::class, 'sendNotification'])->name('send-notification');

Route::post('store-firebase-service', [App\Http\Controllers\HomeController::class,'storeFirebaseService'])->name('store-firebase-service');

Route::post('pay-to-user', [App\Http\Controllers\UserController::class,'payToUser'])->name('pay.user');
Route::post('check-payout-status', [App\Http\Controllers\UserController::class,'checkPayoutStatus'])->name('check.payout.status');

Route::middleware('auth:admin')->group(function () {
    Route::get('/on-board', [App\Http\Controllers\OnBoardController::class, 'index'])->name('on-board');
    Route::get('/on-board/save/{id}', [App\Http\Controllers\OnBoardController::class, 'show'])->name('on-board.save');
});
Route::middleware('auth:admin')->group(function () {
    Route::get('/subscription-plans', [App\Http\Controllers\SubscriptionPlanController::class, 'index'])->name('subscription-plans.index');
    Route::get('/current-subscriber/{id}', [App\Http\Controllers\RestaurantController::class, 'currentSubscriberList'])->name('current-subscriber.list');
    Route::get('/subscription-plans/save/{id?}', [App\Http\Controllers\SubscriptionPlanController::class, 'save'])->name('subscription-plans.save');
    Route::get('/vendor/edit/{id}', [App\Http\Controllers\VendorController::class, 'edit'])->name('vendor.edit');
    Route::get('/vendor/subscription-plan/history/{id?}', [App\Http\Controllers\VendorController::class, 'subscriptionPlanHistory'])->name('vendor.subscriptionPlanHistory');
});

// Vendor data API routes (no middleware for testing)
Route::get('/vendors-data', [App\Http\Controllers\Api\VendorFixedController::class, 'getVendorsData'])->name('vendors.data');
Route::put('/api/vendors/{id}/status', [App\Http\Controllers\VendorController::class, 'updateStatus'])->name('vendors.updateStatus');
Route::delete('/api/vendors/{id}', [App\Http\Controllers\VendorController::class, 'destroy'])->name('vendors.destroy');

// Route removed - using API routes instead

// New restructured database routes
Route::prefix('restructured')->group(function () {
    // Vendor routes using new structure
    Route::get('/vendors-data', [App\Http\Controllers\VendorRestructuredController::class, 'getVendorsData'])->name('restructured.vendors.data');
    Route::get('/vendors/{id}/edit', [App\Http\Controllers\VendorRestructuredController::class, 'getVendorForEdit'])->name('restructured.vendors.edit');
    Route::put('/vendors/{id}/status', [App\Http\Controllers\VendorRestructuredController::class, 'updateStatus'])->name('restructured.vendors.updateStatus');
    Route::delete('/vendors/{id}', [App\Http\Controllers\VendorRestructuredController::class, 'destroy'])->name('restructured.vendors.destroy');

    // Customer routes using new structure
    Route::post('/customers/datatable', [App\Http\Controllers\CustomerRestructuredController::class, 'getCustomersData'])->name('restructured.customers.datatable');

    // Driver routes using new structure
    Route::post('/drivers/datatable', [App\Http\Controllers\DriverRestructuredController::class, 'getDriversData'])->name('restructured.drivers.datatable');
});
Route::get('/restaurantFilters', [App\Http\Controllers\RestaurantFiltersController::class, 'index'])->name('restaurantFilters');
Route::get('/restaurantFilters/create', [App\Http\Controllers\RestaurantFiltersController::class, 'create'])->name('restaurantFilters.create');
Route::get('/restaurantFilters/edit/{id}', [App\Http\Controllers\RestaurantFiltersController::class, 'edit'])->name('restaurantFilters.edit');





Route::get('/create-package', function () {
    return view('new_ui.create-package'); 
});
Route::get('/add-subscription', function () {
    return view('new_ui.add-subscription');
});
Route::get('/change-subscription', function () {
    return view('new_ui.change-subscription');
});
Route::get('/edit-subscription', function () {
    return view('new_ui.edit-subscription');
});

// Removed OSM test route - not needed in production

// Test route to verify dual database setup
Route::get('/test-db', function () {
    $stats = [
        'dual_database_setup' => true,
        'default_connection' => config('database.default'),
        'mysql_connection' => [
            'status' => 'connected',
            'database' => config('database.connections.mysql.database'),
            'tables' => []
        ],
        'postgresql_connection' => [
            'status' => 'connected',
            'database' => config('database.connections.firebase.database'),
            'firebase_data' => [
                'customers' => App\Models\Customer::count(),
                'vendors' => App\Models\Vendor::count(),
                'bookings' => App\Models\BookedTable::count()
            ]
        ]
    ];

    // Test MySQL connection and get table list
    try {
        $mysqlTables = DB::connection('mysql')->select('SHOW TABLES');
        $stats['mysql_connection']['tables'] = array_map(function($table) {
            $tableName = array_values((array)$table)[0];
            return [
                'name' => $tableName,
                'count' => DB::connection('mysql')->table($tableName)->count()
            ];
        }, $mysqlTables);
    } catch (Exception $e) {
        $stats['mysql_connection']['status'] = 'error';
        $stats['mysql_connection']['error'] = $e->getMessage();
    }

    // Test PostgreSQL connection with sample data
    try {
        $recentBookings = App\Models\BookedTable::with(['customer', 'vendor'])
            ->orderBy('createdAt', 'desc')
            ->limit(3)
            ->get()
            ->map(function($booking) {
                return [
                    'id' => $booking->id,
                    'guest_name' => $booking->guestFirstName . ' ' . $booking->guestLastName,
                    'restaurant' => $booking->vendor->title ?? 'N/A',
                    'status' => $booking->status,
                    'date' => $booking->date
                ];
            });
        $stats['postgresql_connection']['sample_bookings'] = $recentBookings;
    } catch (Exception $e) {
        $stats['postgresql_connection']['status'] = 'error';
        $stats['postgresql_connection']['error'] = $e->getMessage();
    }

    return response()->json($stats, 200, [], JSON_PRETTY_PRINT);
});

/*
|--------------------------------------------------------------------------
| Additional Routes for Migrated Pages
|--------------------------------------------------------------------------
*/

// Dynamic Notifications Routes
Route::get('/dynamic_notifications', [App\Http\Controllers\DynamicNotificationController::class, 'index'])->name('dynamic_notifications.index');
Route::get('/dynamic_notifications/create', [App\Http\Controllers\DynamicNotificationController::class, 'create'])->name('dynamic_notifications.create');
Route::post('/dynamic_notifications', [App\Http\Controllers\DynamicNotificationController::class, 'store'])->name('dynamic_notifications.store');
Route::get('/dynamic_notifications/{id}', [App\Http\Controllers\DynamicNotificationController::class, 'show'])->name('dynamic_notifications.show');
Route::get('/dynamic_notifications/{id}/edit', [App\Http\Controllers\DynamicNotificationController::class, 'edit'])->name('dynamic_notifications.edit');
Route::put('/dynamic_notifications/{id}', [App\Http\Controllers\DynamicNotificationController::class, 'update'])->name('dynamic_notifications.update');
Route::delete('/dynamic_notifications/{id}', [App\Http\Controllers\DynamicNotificationController::class, 'destroy'])->name('dynamic_notifications.destroy');

// Email Templates Routes
Route::get('/email_templates', [App\Http\Controllers\EmailTemplateController::class, 'index'])->name('email_templates.index');
Route::get('/email_templates/create', [App\Http\Controllers\EmailTemplateController::class, 'create'])->name('email_templates.create');
Route::post('/email_templates', [App\Http\Controllers\EmailTemplateController::class, 'store'])->name('email_templates.store');
Route::get('/email_templates/{id}', [App\Http\Controllers\EmailTemplateController::class, 'show'])->name('email_templates.show');
Route::get('/email_templates/{id}/edit', [App\Http\Controllers\EmailTemplateController::class, 'edit'])->name('email_templates.edit');
Route::put('/email_templates/{id}', [App\Http\Controllers\EmailTemplateController::class, 'update'])->name('email_templates.update');
Route::delete('/email_templates/{id}', [App\Http\Controllers\EmailTemplateController::class, 'destroy'])->name('email_templates.destroy');

// Booked Table Routes
Route::get('/booked-table', [App\Http\Controllers\BookedTableController::class, 'index'])->name('booked-table.index');
Route::get('/booked-table/create', [App\Http\Controllers\BookedTableController::class, 'create'])->name('booked-table.create');
Route::post('/booked-table', [App\Http\Controllers\BookedTableController::class, 'store'])->name('booked-table.store');
Route::get('/booked-table/{id}', [App\Http\Controllers\BookedTableController::class, 'show'])->name('booked-table.show');
Route::get('/booked-table/{id}/edit', [App\Http\Controllers\BookedTableController::class, 'edit'])->name('booked-table.edit');
Route::put('/booked-table/{id}', [App\Http\Controllers\BookedTableController::class, 'update'])->name('booked-table.update');
Route::delete('/booked-table/{id}', [App\Http\Controllers\BookedTableController::class, 'destroy'])->name('booked-table.destroy');

// Customer Routes
Route::get('/customers', [App\Http\Controllers\CustomerController::class, 'index'])->name('customers.index');
Route::get('/customers/create', [App\Http\Controllers\CustomerController::class, 'create'])->name('customers.create');
Route::post('/customers', [App\Http\Controllers\CustomerController::class, 'store'])->name('customers.store');
Route::get('/customers/{id}', [App\Http\Controllers\CustomerController::class, 'show'])->name('customers.show');
Route::get('/customers/{id}/edit', [App\Http\Controllers\CustomerController::class, 'edit'])->name('customers.edit');
Route::put('/customers/{id}', [App\Http\Controllers\CustomerController::class, 'update'])->name('customers.update');
Route::delete('/customers/{id}', [App\Http\Controllers\CustomerController::class, 'destroy'])->name('customers.destroy');
Route::post('/customers/{id}/add-wallet', [App\Http\Controllers\CustomerController::class, 'addWalletAmount'])->name('customers.add-wallet');
Route::get('/customers/{id}/orders', [App\Http\Controllers\CustomerController::class, 'orders'])->name('customers.orders');
Route::get('/customers/{id}/reviews', [App\Http\Controllers\CustomerController::class, 'reviews'])->name('customers.reviews');
Route::get('/customers/{id}/wallet-transactions', [App\Http\Controllers\CustomerController::class, 'walletTransactions'])->name('customers.wallet-transactions');

// Driver Routes (duplicates removed - using middleware routes above)

// Order Routes (duplicates removed - using middleware routes above)
Route::middleware(['auth'])->group(function () {
    Route::post('/orders/{id}/assign-driver', [App\Http\Controllers\OrderController::class, 'assignDriver'])->name('orders.assign-driver');
    Route::get('/orders/{id}/print', [App\Http\Controllers\OrderController::class, 'orderprint'])->name('orders.orderprint');
    Route::post('/orders/send-notification', [App\Http\Controllers\OrderController::class, 'sendNotification'])->name('orders.send-notification');
});

// Category Routes (duplicates removed - using middleware routes above)

// Zone Routes (duplicates removed - using middleware routes above)

// Commission Management Routes - REMOVED (Integrated into Unified Pricing System)
// All commission management is now handled through /unified-pricing

// Redirect old commission routes to unified pricing system
Route::get('/settings/app/adminCommission', function() {
    return redirect()->route('unified-pricing.index')
        ->with('info', trans('unified_pricing.commission_moved_notice'));
})->middleware('auth:admin')->name('settings.app.adminCommission');

Route::get('/settings/app/adminCommissionOld', function() {
    return redirect()->route('unified-pricing.index')
        ->with('info', trans('unified_pricing.commission_moved_notice'));
})->middleware('auth:admin')->name('settings.app.adminCommissionOld');

Route::get('/settings/app/adminCommissionNew', function() {
    return redirect()->route('unified-pricing.index')
        ->with('info', trans('unified_pricing.commission_moved_notice'));
})->middleware('auth:admin')->name('settings.app.adminCommissionNew');

// Commission test route - REMOVED (Use unified system endpoints)

// Test commission routes - REMOVED (Use unified system endpoints)

// Route لتسجيل الدخول السريع للاختبار
Route::get('/quick-login', function() {
    try {
        // البحث في جدول admin_users
        $adminUser = \App\Models\AdminUser::where('email', '<EMAIL>')->first();

        if (!$adminUser) {
            // إنشاء admin user جديد إذا لم يوجد
            $adminUser = new \App\Models\AdminUser();
            $adminUser->name = 'Admin User';
            $adminUser->email = '<EMAIL>';
            $adminUser->password = Hash::make('password');
            $adminUser->active = 1;
            $adminUser->save();
        }

        // تسجيل الدخول باستخدام admin guard
        Auth::guard('admin')->login($adminUser);

        return redirect()->route('unified-pricing.index')->with('success', 'تم تسجيل الدخول بنجاح');

    } catch (\Exception $e) {
        return response()->json([
            'error' => 'خطأ في تسجيل الدخول: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Route للاختبار المباشر - Admin Authentication Test
Route::get('/test-admin-auth', function() {
    $user = Auth::guard('admin')->user();
    $role_has_permission = [];
    if ($user && $user->role_id) {
        $role_has_permission = App\Models\Permission::where('role_id', $user->role_id)->pluck('permission')->toArray();
    }
    // Add 'all' permission for super admin
    if ($user && $user->role && $user->role->role_name === 'مدير عام') {
        $role_has_permission[] = 'all';
    }

    return response()->json([
        'success' => true,
        'admin_guard_check' => Auth::guard('admin')->check(),
        'admin_user' => $user ? $user->name : null,
        'user_role' => $user && $user->role ? $user->role->role_name : null,
        'has_unified_pricing' => in_array('unified-pricing', $role_has_permission),
        'has_all_permission' => in_array('all', $role_has_permission),
        'unified_pricing_accessible' => in_array('all', $role_has_permission) || in_array('unified-pricing', $role_has_permission),
        'timestamp' => now()
    ]);
});

// تضمين مسارات تحليلات السائقين المحدثة
require __DIR__.'/api_driver_analytics.php';